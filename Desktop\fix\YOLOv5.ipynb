import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] 
plt.rcParams['axes.unicode_minus'] = False 
def visualize_yolo_concept():
    """可视化YOLO检测概念"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 左图：网格划分
    ax1.set_xlim(0, 7)
    ax1.set_ylim(0, 7)
    ax1.set_aspect('equal')
    
    # 绘制7x7网格
    for i in range(8):
        ax1.axhline(i, color='lightgray', linewidth=0.5)
        ax1.axvline(i, color='lightgray', linewidth=0.5)
    
    # 模拟球员和足球位置
    player_positions = [(2.5, 4.5), (5.5, 2.5), (1.5, 6.5)]
    ball_position = (4.5, 5.5)
    
    # 绘制球员（蓝色圆圈）
    for pos in player_positions:
        circle = patches.Circle(pos, 0.3, facecolor='blue', alpha=0.7, label='球员')
        ax1.add_patch(circle)
    
    # 绘制足球（红色圆圈）
    ball_circle = patches.Circle(ball_position, 0.2, facecolor='red', alpha=0.7, label='足球')
    ax1.add_patch(ball_circle)
    
    ax1.set_title('YOLO网格划分\n(7×7网格，每个网格负责检测其中心的目标)', fontsize=12)
    ax1.legend()
    
    # 右图：检测结果
    ax2.set_xlim(0, 7)
    ax2.set_ylim(0, 7)
    ax2.set_aspect('equal')
    
    # 绘制检测框
    for i, pos in enumerate(player_positions):
        rect = patches.Rectangle((pos[0]-0.4, pos[1]-0.6), 0.8, 1.2, 
                               linewidth=2, edgecolor='blue', facecolor='none')
        ax2.add_patch(rect)
        ax2.text(pos[0], pos[1]-0.8, f'球员\n{0.85+i*0.05:.2f}', 
                ha='center', va='top', fontsize=10, color='blue')
    
    # 绘制足球检测框
    ball_rect = patches.Rectangle((ball_position[0]-0.3, ball_position[1]-0.3), 0.6, 0.6,
                                linewidth=2, edgecolor='red', facecolor='none')
    ax2.add_patch(ball_rect)
    ax2.text(ball_position[0], ball_position[1]-0.5, '足球\n0.92', 
            ha='center', va='top', fontsize=10, color='red')
    
    ax2.set_title('YOLO检测结果\n(边界框 + 类别 + 置信度)', fontsize=12)
    
    plt.tight_layout()
    plt.show()


# 运行可视化
visualize_yolo_concept()

import sys
import importlib

def check_package(package_name, version_attr='__version__'):
    """检查包是否正确安装"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, version_attr, 'Unknown')
        print(f"✓ {package_name}: {version}")
        return True
    except ImportError:
        print(f"✗ {package_name}: 未安装")
        return False

def main():
    print("YOLOv5足球检测项目环境检查\n")

    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {python_version}")

    if sys.version_info < (3, 11):
        print(" 警告: 推荐使用Python 3.11+")

    print("\n核心依赖检查:")

    # 精简版核心包检查
    core_packages = [
        'torch', 'torchvision', 'ultralytics', 'cv2',
        'numpy', 'pandas', 'sklearn', 'matplotlib',
        'seaborn', 'yaml', 'requests', 'psutil', 'fastapi'
    ]

    failed_packages = []
    for package in core_packages:
        if not check_package(package):
            failed_packages.append(package)

    # CUDA检查
    try:
        import torch
        print(f"\nCUDA支持:")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError:
        print("\n PyTorch未安装，无法检查CUDA支持")

    # 总结
    print(f"\n检查结果:")
    print(f"   成功: {len(core_packages) - len(failed_packages)}/{len(core_packages)}")

    if failed_packages:
        print(f"   失败的包: {', '.join(failed_packages)}")
        print(f"\n请运行以下命令安装缺失的包:")
        print(f"   pip install {' '.join(failed_packages)}")
        return False
    else:
        print("   所有核心依赖都已正确安装！")
        return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("有依赖缺失，请先安装")



import torch
import psutil
import platform

def check_hardware_requirements():
    """检查硬件配置是否满足要求"""
    print("系统硬件配置检查\n")
    
    # 系统信息
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"处理器: {platform.processor()}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    memory_gb = memory.total / (1024**3)
    print(f"系统内存: {memory_gb:.1f} GB")
    
    if memory_gb >= 16:
        print("内存充足")
    elif memory_gb >= 8:
        print(" 内存勉强够用，建议关闭其他程序")
    else:
        print("内存不足，可能影响训练效果")
    
    # GPU信息
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        print(f"\nGPU配置:")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
            print(f"GPU {i}: {gpu_name}")
            print(f"显存: {gpu_memory:.1f} GB")
            
            if gpu_memory >= 8:
                print("显存充足，可以使用较大的batch size")
            elif gpu_memory >= 6:
                print("显存够用，建议使用中等batch size")
            elif gpu_memory >= 4:
                print(" 显存较少，需要使用小batch size")
            else:
                print("显存不足，建议使用CPU训练或升级显卡")
    else:
        print("\n 未检测到可用GPU，将使用CPU训练（速度较慢）")
    
    print("\n" + "="*50)
    print("硬件配置建议:")
    print("   - 如果显存不足，可以减小batch size (--batch-size 4)")
    print("   - 如果内存不足，可以减少数据加载进程 (--workers 2)")
    print("   - 训练时关闭不必要的程序以释放资源")

# 运行硬件检查
check_hardware_requirements()

import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
import numpy as np
import requests
from PIL import Image
import io
import os
import time
os.environ['KMP_DUPLICATE_LIB_OK']='TRUE'

# 设置matplotlib在Jupyter Notebook中显示
%matplotlib inline

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def download_file_with_retry(url, local_path, max_retries=3):
    """带重试机制的文件下载函数"""
    for attempt in range(max_retries):
        try:
            print(f"尝试下载 {url} (尝试 {attempt + 1}/{max_retries})...")
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                f.write(response.content)
                
            print(f"✓ 下载成功: {local_path}")
            return True
            
        except Exception as e:
            print(f"下载失败: {e}")
            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2  # 指数退避
                print(f"等待 {wait_time}秒后重试...")
                time.sleep(wait_time)
    
    return False

def download_test_images():
    """下载测试图片到本地"""
    test_images_dir = 'test_images'
    os.makedirs(test_images_dir, exist_ok=True)
    
    # 测试图片URL和本地文件名
    image_urls = {
        'bus.jpg': 'https://ultralytics.com/images/bus.jpg',
        'zidane.jpg': 'https://ultralytics.com/images/zidane.jpg'
    }
    
    local_images = []
    
    for filename, url in image_urls.items():
        local_path = os.path.join(test_images_dir, filename)
        
        # 检查文件是否已存在
        if os.path.exists(local_path):
            print(f"✓ {filename} 已存在，跳过下载")
            local_images.append(local_path)
        else:
            if download_file_with_retry(url, local_path):
                local_images.append(local_path)
            else:
                print(f"⚠ 无法下载 {filename}, 将直接使用URL")
                local_images.append(url)
    
    return local_images

def load_yolo_model(model_name='yolov5nu.pt', fallback_model='yolov5n.pt'):
    """加载YOLO模型，带有备用方案"""
    try:
        print(f"尝试加载模型: {model_name}")
        model = YOLO(model_name)
        print(f"✓ 成功加载模型: {model_name}")
        return model
    except Exception as e:
        print(f"无法加载 {model_name}: {e}")
        if model_name != fallback_model:
            print(f"尝试加载备用模型: {fallback_model}")
            try:
                model = YOLO(fallback_model)
                print(f"✓ 成功加载备用模型: {fallback_model}")
                return model
            except Exception as e2:
                print(f"无法加载备用模型: {e2}")
        raise RuntimeError("无法加载任何YOLO模型")

def ultralytics_hello_yolo_test():
    """Ultralytics YOLOv5 目标检测并直接显示结果"""
    
    try:        
        # 1. 加载预训练模型（优先尝试yolov5nu.pt）
        model = load_yolo_model('yolov5nu.pt', 'yolov5n.pt')
        print("模型加载成功！\n")
        
        # 2. 准备测试图片（下载到本地）
        print("准备测试图片...")
        test_images = download_test_images()
        print(f"准备完成，共 {len(test_images)} 张图片\n")
        
        # 3. 执行检测（不保存文件）
        print("开始目标检测...")
        results = model(test_images, 
                       conf=0.25,           # 置信度阈值
                       save=False,          # 不保存结果图片到文件
                       verbose=False)       # 减少输出信息
        
        print("检测完成！\n")
        
        # 4. 显示检测结果统计
        print("检测结果统计:")
        total_detections = 0
        for i, result in enumerate(results):
            detections = len(result.boxes) if result.boxes is not None else 0
            total_detections += detections
            print(f"   图片 {i+1}: 检测到 {detections} 个目标")
        
        print(f"   总计检测到: {total_detections} 个目标\n")
        
        # 5. 在Jupyter中直接显示结果图片
        display_results_in_notebook(results)
        
        print("目标检测成功完成！")
        return True
        
    except Exception as e:
        print(f"检测失败: {e}")
        print("\n可能的解决方案:")
        print("   1. 确保网络连接正常（需要下载模型和测试图片）")
        print("   2. 检查ultralytics包是否正确安装: pip install --upgrade ultralytics")
        print("   3. 确保PyTorch安装正确")
        print("   4. 检查requests包是否安装: pip install requests")
        print("   5. 尝试手动下载模型文件并放在当前目录")
        print("      - yolov5nu.pt: https://github.com/ultralytics/assets/releases")
        return False

def display_results_in_notebook(results):
    """在Jupyter Notebook中显示检测结果"""
    num_images = len(results)
    
    # 设置画布布局 - 改为上下分布，图片尺寸缩小一半
    if num_images == 1:
        fig, ax = plt.subplots(1, 1, figsize=(8, 6))  # 稍微增大单张图片尺寸
        axes = [ax]
    elif num_images == 2:
        fig, axes = plt.subplots(2, 1, figsize=(10, 10))  # 上下分布，适当增大
    else:
        num_images = min(4, num_images)
        fig, axes = plt.subplots(4, 1, figsize=(10, 16))  # 上下分布，适当增大
    
    # 确保axes是列表格式
    if not isinstance(axes, (list, np.ndarray)):
        axes = [axes]
    
    # 遍历结果并显示
    for i in range(num_images):
        try:
            # 直接从结果中获取绘制好的图像（BGR格式）
            result_img = results[i].plot()  # 返回numpy数组，BGR格式
            
            # 转换为RGB格式（matplotlib需要RGB格式）
            result_img_rgb = result_img[..., ::-1]
            
            # 显示图像
            axes[i].imshow(result_img_rgb)
            axes[i].set_title(f"检测结果 {i+1}", fontsize=14, pad=15)
            axes[i].axis('off')  # 关闭坐标轴
            
        except Exception as e:
            axes[i].text(0.5, 0.5, f"无法加载图片\n{str(e)}", 
                        ha='center', va='center', 
                        transform=axes[i].transAxes, 
                        fontsize=12)
            axes[i].set_title(f"图片 {i+1} 加载失败")
            axes[i].axis('off')
    
    # 隐藏多余的子图
    if num_images < len(axes):
        for i in range(num_images, len(axes)):
            axes[i].axis('off')
    
    # 设置总标题和布局
    plt.suptitle('YOLOv5检测结果展示', fontsize=18, y=1)
    plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为总标题留出空间
    
    # 确保图片在notebook中居中显示
    plt.subplots_adjust(left=0.1, right=0.9, top=0.92, bottom=0.08)
    
    plt.show()

if __name__ == "__main__":
    hello_success = ultralytics_hello_yolo_test()

def demonstrate_yolo_format():
    """演示YOLO坐标格式转换"""
    print("YOLO坐标格式演示\n")
    
    # 示例图片尺寸
    img_width, img_height = 640, 480
    print(f"示例图片尺寸: {img_width} × {img_height}")
    
    # 示例边界框（像素坐标）
    examples = [
        {"name": "球员1", "class_id": 0, "x_center": 320, "y_center": 240, "width": 100, "height": 150},
        {"name": "足球", "class_id": 1, "x_center": 500, "y_center": 350, "width": 30, "height": 30},
        {"name": "球员2", "class_id": 0, "x_center": 150, "y_center": 200, "width": 80, "height": 120}
    ]
    
    print("\n像素坐标 → YOLO格式转换:")
    print("-" * 80)
    print(f"{'目标':<8} {'像素坐标':<25} {'YOLO格式':<30} {'验证':<10}")
    print("-" * 80)
    
    for example in examples:
        # 转换为YOLO格式
        x_norm = example['x_center'] / img_width
        y_norm = example['y_center'] / img_height
        w_norm = example['width'] / img_width
        h_norm = example['height'] / img_height
        
        # 验证范围
        valid = all(0 <= val <= 1 for val in [x_norm, y_norm, w_norm, h_norm])
        
        pixel_str = f"({example['x_center']},{example['y_center']},{example['width']},{example['height']})"
        yolo_str = f"{example['class_id']} {x_norm:.3f} {y_norm:.3f} {w_norm:.3f} {h_norm:.3f}"
        status = "" if valid else "❌"
        
        print(f"{example['name']:<8} {pixel_str:<25} {yolo_str:<30} {status:<10}")
    
    print("-" * 80)
    print("\n关键要点:")
    print("   - 所有YOLO坐标值必须在 [0, 1] 范围内")
    print("   - 坐标表示的是边界框的中心点，不是左上角")
    print("   - width和height是边界框的宽度和高度，不是右下角坐标")
    print("   - 类别ID从0开始：0=球员, 1=足球")

# 运行演示
demonstrate_yolo_format()

import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import random
import numpy as np
import os
import glob

# 设置matplotlib在Jupyter Notebook中显示
%matplotlib inline

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def visualize_random_samples(num_samples=4):

    # 使用正确的数据集路径
    train_img_dir = 'dataset/train/images'
    train_lbl_dir = 'dataset/train/labels'
    
    if not os.path.exists(train_img_dir):
        print("训练图片目录不存在，请检查数据集路径")
        return
    
    # 获取所有训练图片
    image_files = glob.glob(os.path.join(train_img_dir, '*.jpg'))
    if len(image_files) == 0:
        print("训练目录中没有图片文件")
        return
    
    # 随机选择样本
    selected_files = random.sample(image_files, min(num_samples, len(image_files)))
    
    # 类别配置（根据data.yaml）
    class_names = {0: 'ball', 1: 'goalkeeper', 2: 'player', 3: 'referee'}
    class_colors = {0: 'red', 1: 'blue', 2: 'green', 3: 'orange'}
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(12, 7))
    axes = axes.flatten()
    
    for idx, img_path in enumerate(selected_files):
        ax = axes[idx]
        
        try:
            # 读取图片
            img = cv2.imread(img_path)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img_height, img_width = img.shape[:2]
            
            ax.imshow(img)
            
            # 读取标签文件
            img_name = os.path.splitext(os.path.basename(img_path))[0]
            label_path = os.path.join(train_lbl_dir, f'{img_name}.txt')
            
            bbox_count = 0
            
            if os.path.exists(label_path):
                with open(label_path, 'r') as f:
                    lines = f.readlines()
                
                for line in lines:
                    parts = line.strip().split()
                    if len(parts) == 5:
                        class_id = int(parts[0])
                        x_center, y_center, width, height = map(float, parts[1:5])
                        
                        # 转换为像素坐标
                        x_center_px = x_center * img_width
                        y_center_px = y_center * img_height
                        width_px = width * img_width
                        height_px = height * img_height
                        
                        # 计算边界框坐标
                        x_min = x_center_px - width_px / 2
                        y_min = y_center_px - height_px / 2
                        
                        # 绘制边界框
                        rect = patches.Rectangle(
                            (x_min, y_min), width_px, height_px,
                            linewidth=2,
                            edgecolor=class_colors.get(class_id, 'yellow'),
                            facecolor='none'
                        )
                        ax.add_patch(rect)
                        
                        # 添加类别标签
                        ax.text(x_min, y_min-5,
                               class_names.get(class_id, f'Class{class_id}'),
                               color=class_colors.get(class_id, 'yellow'),
                               fontsize=9, fontweight='bold',
                               bbox=dict(boxstyle='round,pad=0.2',
                                       facecolor='white', alpha=0.8))
                        
                        bbox_count += 1
            
            # 设置标题 - 修改为Fig 1到Fig 4格式
            ax.set_title(f"Fig {idx+1} 目标数: {bbox_count}", fontsize=10)
            ax.axis('off')
            
        except Exception as e:
            ax.text(0.5, 0.5, f"加载失败\n{str(e)}",
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f"Fig {idx+1} 加载失败")
            ax.axis('off')
    
    # 隐藏多余的子图
    for idx in range(len(selected_files), 4):
        axes[idx].axis('off')
    
    plt.tight_layout()
    plt.suptitle('随机样本可视化', fontsize=14, y=1)
    plt.show()
    
    print(f"成功可视化 {len(selected_files)} 个样本")
    print("\n数据集统计:")
    print(f"   总训练图片: {len(image_files)} 张")
    print(f"   类别: {list(class_names.values())}")

# 执行可视化
visualize_random_samples(4)

def analyze_label_distribution():
    """分析标签分布情况"""
    print("分析标签分布情况\n")
    
    # 分析训练集、验证集和测试集
    splits = ['train', 'valid', 'test']
    class_names = {0: 'ball', 1: 'goalkeeper', 2: 'player', 3: 'referee'}
    
    for split in splits:
        label_dir = f'dataset/{split}/labels'
        
        if not os.path.exists(label_dir):
            print(f"{split} 标签目录不存在")
            continue
        
        label_files = glob.glob(os.path.join(label_dir, '*.txt'))
        
        # 统计信息
        class_count = {0: 0, 1: 0, 2: 0, 3: 0}  # ball, goalkeeper, player, referee
        total_bboxes = 0
        files_with_labels = 0
        empty_files = 0
        
        print(f"分析 {split} 集 ({len(label_files)} 个标签文件)...")
        
        for label_file in label_files:
            try:
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                
                if len(lines) == 0:
                    empty_files += 1
                    continue
                
                files_with_labels += 1
                
                for line in lines:
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        if class_id in class_count:
                            class_count[class_id] += 1
                            total_bboxes += 1
                        
            except Exception as e:
                print(f"读取文件 {label_file} 时出错: {e}")
        
        # 显示统计结果
        print(f"\n{split.upper()} 集统计:")
        print(f"   总标签文件: {len(label_files)}")
        print(f"   有标注文件: {files_with_labels}")
        print(f"   空标注文件: {empty_files}")
        print(f"   总目标数量: {total_bboxes}")
        
        print(f"\n类别分布:")
        for class_id, count in class_count.items():
            class_name = class_names.get(class_id, f'Class{class_id}')
            percentage = (count / total_bboxes * 100) if total_bboxes > 0 else 0
            print(f"   {class_name}: {count} ({percentage:.1f}%)")
        
        print("-" * 50)
    
    print("\n标签分布分析完成")

# 执行分析
analyze_label_distribution()

import matplotlib.pyplot as plt
import numpy as np

# 设置matplotlib在Jupyter Notebook中显示
%matplotlib inline

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def demonstrate_overfitting():
    """演示明显的过拟合现象"""
    print("过拟合现象明显演示\n")
    
    # 模拟训练过程中的损失变化
    epochs = np.arange(1, 201)
    
    # 正常训练曲线 - 平稳收敛
    train_loss_normal = 1.5 * np.exp(-epochs/30) + 0.2 + 0.05 * np.random.normal(0, 0.1, len(epochs))
    val_loss_normal = 1.6 * np.exp(-epochs/35) + 0.25 + 0.05 * np.random.normal(0, 0.1, len(epochs))
    
    # 过拟合训练曲线 - 明显分离
    train_loss_overfit = 2.0 * np.exp(-epochs/20) + 0.05 + 0.03 * np.random.normal(0, 0.1, len(epochs))
    val_loss_overfit = 2.0 * np.exp(-epochs/25) + 0.3
    
    # 验证集损失在第50轮后开始明显上升（过拟合开始）
    overfit_start = 50
    for i in range(overfit_start, len(epochs)):
        # 验证集损失逐渐上升，模拟过拟合
        val_loss_overfit[i] = val_loss_overfit[overfit_start-1] + 0.008 * (i - overfit_start + 1) + 0.1 * np.sin((i-overfit_start)/10)
    
    # 添加噪声使曲线更真实
    val_loss_overfit += 0.05 * np.random.normal(0, 0.1, len(epochs))
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 7))
    
    # 正常训练
    ax1.plot(epochs, train_loss_normal, 'b-', label='训练集损失', linewidth=2.5, alpha=0.8)
    ax1.plot(epochs, val_loss_normal, 'r-', label='验证集损失', linewidth=2.5, alpha=0.8)
    ax1.fill_between(epochs, train_loss_normal, val_loss_normal, alpha=0.2, color='green')
    ax1.set_xlabel('训练轮数 (Epochs)', fontsize=12)
    ax1.set_ylabel('损失值 (Loss)', fontsize=12)
    ax1.set_title('正常训练 - 良好泛化\n训练集和验证集损失同步下降', fontsize=14, color='green', pad=20)
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 2.0)
    
    # 过拟合训练
    ax2.plot(epochs, train_loss_overfit, 'b-', label='训练集损失', linewidth=2.5, alpha=0.8)
    ax2.plot(epochs, val_loss_overfit, 'r-', label='验证集损失', linewidth=2.5, alpha=0.8)
    ax2.axvline(x=overfit_start, color='orange', linestyle='--', linewidth=3, alpha=0.8, label='过拟合开始点')
    
    # 突出显示过拟合区域
    ax2.fill_between(epochs[overfit_start:], train_loss_overfit[overfit_start:], 
                     val_loss_overfit[overfit_start:], alpha=0.3, color='red', label='过拟合区域')
    
    ax2.set_xlabel('训练轮数 (Epochs)', fontsize=12)
    ax2.set_ylabel('损失值 (Loss)', fontsize=12)
    ax2.set_title('严重过拟合 - 泛化能力差\n验证集损失反弹上升', fontsize=14, color='red', pad=20)
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 2.0)
    
    # 添加注释箭头
    ax2.annotate('验证集损失开始上升！', xy=(100, val_loss_overfit[100]), xytext=(130, 1.5),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=12, color='red', weight='bold')
    
    plt.tight_layout()
    plt.show()
    
    # 计算并显示具体数值
    final_train_normal = train_loss_normal[-1]
    final_val_normal = val_loss_normal[-1]
    final_train_overfit = train_loss_overfit[-1]
    final_val_overfit = val_loss_overfit[-1]
    
    print("最终损失对比:")
    print(f"正常训练 - 训练集: {final_train_normal:.3f}, 验证集: {final_val_normal:.3f}, 差距: {abs(final_train_normal-final_val_normal):.3f}")
    print(f"过拟合训练 - 训练集: {final_train_overfit:.3f}, 验证集: {final_val_overfit:.3f}, 差距: {abs(final_train_overfit-final_val_overfit):.3f}")

# 运行明显的过拟合演示
demonstrate_overfitting()

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import random
import matplotlib.pyplot as plt
import glob
import os

# 设置matplotlib在Jupyter Notebook中显示
%matplotlib inline

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] 
plt.rcParams['axes.unicode_minus'] = False 

def demonstrate_real_augmentations():

    # 从数据集中随机选择一张图片
    train_img_dir = 'dataset/train/images'
    
    if not os.path.exists(train_img_dir):
        print("训练图片目录不存在，请检查数据集路径")
        return
    
    image_files = glob.glob(os.path.join(train_img_dir, '*.jpg'))
    if len(image_files) == 0:
        print("训练目录中没有图片文件")
        return
    
    # 随机选择一张图片
    selected_img_path = random.choice(image_files)
    print(f"选择图片: {os.path.basename(selected_img_path)}")
    
    # 读取原始图片
    original_img = cv2.imread(selected_img_path)
    original_img_rgb = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
    
    # 各种明显的增强方法
    def apply_strong_augmentations(img):
        augmentations = {}
        height, width = img.shape[:2]
        center = (width//2, height//2)
        
        # 1. 水平翻转
        augmentations['水平翻转'] = cv2.flip(img, 1)
        
        # 2. 明显旋转 (30度)
        rotation_matrix = cv2.getRotationMatrix2D(center, 30, 1.0)
        augmentations['旋转30°'] = cv2.warpAffine(img, rotation_matrix, (width, height),
                                                borderMode=cv2.BORDER_REFLECT)
        
        # 3. 明显缩放 (1.5倍)
        scale_matrix = cv2.getRotationMatrix2D(center, 0, 1.5)
        augmentations['缩放1.5x'] = cv2.warpAffine(img, scale_matrix, (width, height))
        
        # 4. 大幅增加亮度
        bright_img = cv2.convertScaleAbs(img, alpha=1.0, beta=80)
        augmentations['大幅增亮'] = bright_img
        
        # 5. 大幅降低亮度
        dark_img = cv2.convertScaleAbs(img, alpha=0.5, beta=-50)
        augmentations['大幅变暗'] = dark_img
        
        # 6. 高对比度
        high_contrast = cv2.convertScaleAbs(img, alpha=2.0, beta=0)
        augmentations['高对比度'] = high_contrast
        
        # 7. 色彩饱和度调整
        hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV).astype(np.float32)
        hsv[:,:,1] = hsv[:,:,1] * 2.0  # 饱和度增强2倍
        hsv[:,:,1] = np.clip(hsv[:,:,1], 0, 255)
        saturated_img = cv2.cvtColor(hsv.astype(np.uint8), cv2.COLOR_HSV2RGB)
        augmentations['高饱和度'] = saturated_img
        
        # 8. 添加高斯噪声
        noise = np.random.normal(0, 50, img.shape).astype(np.uint8)
        noisy_img = cv2.add(img, noise)
        augmentations['添加噪声'] = noisy_img
        
        # 9. 高斯模糊
        blurred_img = cv2.GaussianBlur(img, (15, 15), 0)
        augmentations['高斯模糊'] = blurred_img
        
        # 10. 色调大幅调整
        hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
        hsv[:,:,0] = (hsv[:,:,0] + 60) % 180  # 色调偏移60度
        hue_shifted = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        augmentations['色调偏移'] = hue_shifted
        
        # 11. 透视变换
        pts1 = np.float32([[0, 0], [width, 0], [0, height], [width, height]])
        pts2 = np.float32([[width*0.1, height*0.1], [width*0.9, height*0.05],
                          [width*0.05, height*0.9], [width*0.95, height*0.95]])
        perspective_matrix = cv2.getPerspectiveTransform(pts1, pts2)
        perspective_img = cv2.warpPerspective(img, perspective_matrix, (width, height))
        augmentations['透视变换'] = perspective_img
        
        return augmentations
    
    # 应用增强
    augmented_imgs = apply_strong_augmentations(original_img_rgb)
    
    # 显示结果 (4x3布局) - 调整间距
    fig, axes = plt.subplots(3, 4, figsize=(20, 8))
    axes = axes.flatten()
    
    # 显示原始图片
    axes[0].imshow(original_img_rgb)
    axes[0].set_title('原始图片', fontsize=14, fontweight='bold', color='black')
    axes[0].axis('off')
    
    # 显示增强后的图片 - 修复颜色索引问题
    colors = ['black' for i in range(11)]
    for i, (name, aug_img) in enumerate(augmented_imgs.items()):
        axes_index = i + 1  # 从第二个子图开始
        if axes_index < len(axes):
            axes[axes_index].imshow(aug_img)
            # 修复颜色索引，确保每个标题都有不同颜色
            color_index = i % len(colors)  # 使用模运算确保索引不越界
            axes[axes_index].set_title(f'{name}', fontsize=12, fontweight='bold',
                                     color=colors[color_index])
            axes[axes_index].axis('off')
    
    # 隐藏多余的子图
    for i in range(len(augmented_imgs) + 1, len(axes)):
        axes[i].axis('off')
    
    # 调整布局，减小上下间距
    plt.subplots_adjust(top=0.92, bottom=0.05, hspace=0.15, wspace=0.1)
    plt.suptitle('数据增强效果展示', fontsize=18, fontweight='bold')
    plt.show()

# 运行真实数据增强演示
demonstrate_real_augmentations()

import cv2
import numpy as np
import random
import matplotlib.pyplot as plt
import glob
from ultralytics.data.augment import Mosaic
from ultralytics.data.dataset import YOLODataset
from ultralytics.utils import LOGGER
import torch
from pathlib import Path
import os
os.environ['KMP_DUPLICATE_LIB_OK']='TRUE'

# 设置matplotlib在Jupyter Notebook中显示
%matplotlib inline

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

def demonstrate_ultralytics_mosaic():
    # 数据集路径
    dataset_path = 'dataset'
    train_img_dir = os.path.join(dataset_path, 'train', 'images')
    train_label_dir = os.path.join(dataset_path, 'train', 'labels')
    
    if not os.path.exists(train_img_dir):
        print("训练图片目录不存在，请检查数据集路径")
        return
    
    # 获取图片文件列表
    image_files = glob.glob(os.path.join(train_img_dir, '*.jpg'))
    if len(image_files) < 4:
        print(f"需要至少4张图片进行Mosaic，当前只有{len(image_files)}张")
        return
    
    # 随机选择4张图片
    selected_images = random.sample(image_files, 4)
    
    def load_image_with_labels(img_path):
        """加载图片和对应的标签"""
        # 读取图片
        img = cv2.imread(img_path)
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # 读取对应的标签文件
        label_path = img_path.replace('images', 'labels').replace('.jpg', '.txt')
        labels = []
        if os.path.exists(label_path):
            with open(label_path, 'r') as f:
                for line in f.readlines():
                    parts = line.strip().split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        labels.append([class_id, x_center, y_center, width, height])
        
        return img_rgb, labels
    
    def draw_labels_on_image(img, labels, class_names):
        """在图片上绘制标签框"""
        img_with_labels = img.copy()
        h, w = img.shape[:2]
        
        colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # 不同类别的颜色
        
        for label in labels:
            class_id, x_center, y_center, width, height = label
            
            # 转换为像素坐标
            x1 = int((x_center - width/2) * w)
            y1 = int((y_center - height/2) * h)
            x2 = int((x_center + width/2) * w)
            y2 = int((y_center + height/2) * h)
            
            # 绘制边界框
            color = colors[class_id % len(colors)]
            cv2.rectangle(img_with_labels, (x1, y1), (x2, y2), color, 2)
            
            # 添加类别标签
            class_name = class_names.get(class_id, f'Class_{class_id}')
            cv2.putText(img_with_labels, class_name, (x1, y1-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return img_with_labels
    
    def create_simple_mosaic(images_with_labels, target_size=640):
        # 随机选择拼接点
        split_x = random.randint(int(target_size * 0.25), int(target_size * 0.75))
        split_y = random.randint(int(target_size * 0.25), int(target_size * 0.75))
        
        # 创建空白画布
        mosaic = np.zeros((target_size, target_size, 3), dtype=np.uint8)
        
        # 定义4个区域
        regions = [
            (0, 0, split_x, split_y),                    # 左上
            (split_x, 0, target_size, split_y),          # 右上
            (0, split_y, split_x, target_size),          # 左下
            (split_x, split_y, target_size, target_size)  # 右下
        ]
        
        mosaic_labels = []
        
        for i, ((img, labels), (x1, y1, x2, y2)) in enumerate(zip(images_with_labels, regions)):
            # 调整图片大小
            region_w, region_h = x2 - x1, y2 - y1
            resized_img = cv2.resize(img, (region_w, region_h))
            
            # 放置图片
            mosaic[y1:y2, x1:x2] = resized_img
            
            # 调整标签坐标
            orig_h, orig_w = img.shape[:2]
            scale_x = region_w / orig_w
            scale_y = region_h / orig_h
            
            for label in labels:
                class_id, x_center, y_center, width, height = label
                
                # 转换到新的坐标系
                new_x = (x_center * orig_w * scale_x + x1) / target_size
                new_y = (y_center * orig_h * scale_y + y1) / target_size
                new_w = width * scale_x * orig_w / target_size
                new_h = height * scale_y * orig_h / target_size
                
                # 确保坐标在有效范围内
                if 0 <= new_x <= 1 and 0 <= new_y <= 1 and new_w > 0 and new_h > 0:
                    mosaic_labels.append([class_id, new_x, new_y, new_w, new_h])
        
        return mosaic, mosaic_labels, (split_x, split_y)
    
    # 类别名称映射
    class_names = {0: 'ball', 1: 'goalkeeper', 2: 'player', 3: 'referee'}
    
    # 加载4张图片和标签
    images_with_labels = []
    for img_path in selected_images:
        img, labels = load_image_with_labels(img_path)
        images_with_labels.append((img, labels))
    
    # 创建Mosaic
    mosaic_img, mosaic_labels, split_point = create_simple_mosaic(images_with_labels)
    
    # 在Mosaic图片上绘制标签
    mosaic_with_labels = draw_labels_on_image(mosaic_img, mosaic_labels, class_names)
    
    # 修复后的可视化布局 - 使用subplots替代gridspec
    fig, axes = plt.subplots(2, 3, figsize=(18, 6))
    
    # 显示原始4张图片（带标签）- 2x2布局在左侧
    positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
    for i, ((img, labels), pos) in enumerate(zip(images_with_labels, positions)):
        row, col = pos
        img_with_labels = draw_labels_on_image(img, labels, class_names)
        axes[row, col].imshow(img_with_labels)
        axes[row, col].set_title(f'原图 {i+1}\n({len(labels)} 个目标)', fontsize=12, pad=10)
        axes[row, col].axis('off')
    
    # 隐藏右侧第三列的上下两个子图，为Mosaic图腾出空间
    axes[0, 2].axis('off')
    axes[1, 2].axis('off')
    
    # 在右侧创建一个大的子图显示Mosaic结果
    # 使用subplot2grid创建跨越两行的子图
    ax_mosaic = plt.subplot2grid((2, 3), (0, 2), rowspan=2, fig=fig)
    ax_mosaic.imshow(mosaic_with_labels)
    ax_mosaic.set_title(f'Mosaic数据增强结果\n({len(mosaic_labels)} 个目标)', 
                       fontsize=14, fontweight='bold', color='red', pad=15)
    ax_mosaic.axis('off')
    
    
    # 手动调整布局
    plt.subplots_adjust(left=0.05, right=0.95, top=0.9, bottom=0.05, 
                       wspace=0.2, hspace=0.3)
    plt.show()
    
    # 按类别统计
    class_counts = {}
    for label in mosaic_labels:
        class_id = int(label[0])
        class_name = class_names.get(class_id, f'Class_{class_id}')
        class_counts[class_name] = class_counts.get(class_name, 0) + 1
    
    print(f"\n目标类别分布:")
    for class_name, count in class_counts.items():
        print(f"   {class_name}: {count} 个")


demonstrate_ultralytics_mosaic()

import warnings
warnings.filterwarnings('ignore')
import matplotlib.pyplot as plt
import numpy as np
from ultralytics import YOLO
import torch
import os
os.environ['KMP_DUPLICATE_LIB_OK']='TRUE'

# 设置matplotlib在Jupyter Notebook中显示
%matplotlib inline

# plt.rcParams['font.sans-serif'] = ['Microsoft YaHei'] 
# plt.rcParams['axes.unicode_minus'] = False 


# 使用系统可用的中文字体
available_chinese_fonts = ['Microsoft YaHei', 'SimHei', 'Microsoft JhengHei', 'STXihei', 'SimSun']
selected_font = None

# 检测并选择第一个可用的字体
for font in available_chinese_fonts:
    try:
        plt.rcParams['font.sans-serif'] = [font]
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        # 测试字体是否真的可用
        test_fig, test_ax = plt.subplots()
        test_ax.set_title('字体测试 ' + font)
        plt.close(test_fig)
        selected_font = font
        print(f"已选择字体: {font}")
        break
    except:
        continue

if selected_font is None:
    print("警告: 未找到可用的中文字体，将使用默认字体")
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']  # 回退到英文字体





def simulate_model_info():
    """模拟显示模型信息"""
    print("\n=== 模型配置信息 ===")
    print("Model: YOLOv5n (Nano version)")
    print("Parameters: ~1.9M")
    print("GFLOPs: ~4.5")
    print("Dataset: Custom Football Dataset")
    print("Classes: ['ball', 'goalkeeper', 'player', 'referee']")
    print("\nNote: 这是训练过程的可视化演示，使用YOLOv5n默认配置")

def explain_ultralytics_training_process():
    """解释Ultralytics YOLO训练过程和监控指标"""
    print("Ultralytics YOLOv5n训练过程详解\n")
    
    # YOLOv5n默认训练配置
    training_config = {
        'model': 'yolov5n.pt',
        'epochs': 100,  # YOLOv5n默认epochs
        'batch': 16,
        'imgsz': 640,
        'optimizer': 'SGD',
        'lr0': 0.01,
        'weight_decay': 0.0005,
        'momentum': 0.937,
        'warmup_epochs': 3,
        'close_mosaic': 10
    }
    
    print(f"YOLOv5n训练配置: {training_config}\n")
    
    # 创建训练流程可视化
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 模拟训练损失曲线（基于100个epochs）
    epochs = np.arange(1, training_config['epochs'] + 1)
    
    # YOLOv5n风格的损失曲线（更快收敛）
    train_box_loss = 0.08 * np.exp(-epochs/25) + 0.015 + 0.003 * np.random.normal(0, 1, training_config['epochs'])
    train_obj_loss = 0.6 * np.exp(-epochs/20) + 0.08 + 0.015 * np.random.normal(0, 1, training_config['epochs'])
    train_cls_loss = 0.4 * np.exp(-epochs/30) + 0.05 + 0.01 * np.random.normal(0, 1, training_config['epochs'])
    
    val_box_loss = 0.09 * np.exp(-epochs/28) + 0.018 + 0.004 * np.random.normal(0, 1, training_config['epochs'])
    val_obj_loss = 0.65 * np.exp(-epochs/22) + 0.09 + 0.018 * np.random.normal(0, 1, training_config['epochs'])
    val_cls_loss = 0.45 * np.exp(-epochs/32) + 0.06 + 0.012 * np.random.normal(0, 1, training_config['epochs'])
    
    # 确保损失值为正数，避免负号问题
    train_box_loss = np.abs(train_box_loss)
    train_obj_loss = np.abs(train_obj_loss)
    train_cls_loss = np.abs(train_cls_loss)
    val_box_loss = np.abs(val_box_loss)
    val_obj_loss = np.abs(val_obj_loss)
    val_cls_loss = np.abs(val_cls_loss)
    
    ax1.plot(epochs, train_box_loss, 'b-', label='train/box_loss', alpha=0.8, linewidth=1.5)
    ax1.plot(epochs, train_obj_loss, 'g-', label='train/obj_loss', alpha=0.8, linewidth=1.5)
    ax1.plot(epochs, train_cls_loss, 'r-', label='train/cls_loss', alpha=0.8, linewidth=1.5)
    ax1.plot(epochs, val_box_loss, 'b--', label='val/box_loss', alpha=0.7, linewidth=1.5)
    ax1.plot(epochs, val_obj_loss, 'g--', label='val/obj_loss', alpha=0.7, linewidth=1.5)
    ax1.plot(epochs, val_cls_loss, 'r--', label='val/cls_loss', alpha=0.7, linewidth=1.5)
    
    ax1.set_xlabel('Epochs')
    ax1.set_ylabel('Loss')
    ax1.set_title('YOLOv5n Loss Curves')
    ax1.legend(fontsize=9)
    ax1.grid(True, alpha=0.3)
    ax1.set_xlim(0, training_config['epochs'])
    # 设置y轴范围避免负值
    ax1.set_ylim(bottom=0)
    
    # 2. 模拟mAP变化（YOLOv5n风格）
    map50 = 0.75 * (1 - np.exp(-epochs/35)) + 0.04 * np.random.normal(0, 1, training_config['epochs'])
    map50_95 = 0.55 * (1 - np.exp(-epochs/40)) + 0.03 * np.random.normal(0, 1, training_config['epochs'])
    map50 = np.clip(map50, 0, 1)
    map50_95 = np.clip(map50_95, 0, 1)
    
    ax2.plot(epochs, map50, 'purple', linewidth=2, label='mAP@0.5')
    ax2.plot(epochs, map50_95, 'orange', linewidth=2, label='mAP@0.5:0.95')
    ax2.set_xlabel('Epochs')
    ax2.set_ylabel('mAP')
    ax2.set_title('Mean Average Precision (mAP)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1)
    ax2.set_xlim(0, training_config['epochs'])
    
    # 3. 精确率和召回率（按类别）
    precision_ball = 0.85 * (1 - np.exp(-epochs/25)) + 0.02 * np.random.normal(0, 1, training_config['epochs'])
    precision_player = 0.78 * (1 - np.exp(-epochs/30)) + 0.025 * np.random.normal(0, 1, training_config['epochs'])
    recall_ball = 0.82 * (1 - np.exp(-epochs/28)) + 0.02 * np.random.normal(0, 1, training_config['epochs'])
    recall_player = 0.75 * (1 - np.exp(-epochs/32)) + 0.03 * np.random.normal(0, 1, training_config['epochs'])
    
    precision_ball = np.clip(precision_ball, 0, 1)
    precision_player = np.clip(precision_player, 0, 1)
    recall_ball = np.clip(recall_ball, 0, 1)
    recall_player = np.clip(recall_player, 0, 1)
    
    ax3.plot(epochs, precision_ball, 'blue', label='Precision (Ball)', linewidth=2)
    ax3.plot(epochs, precision_player, 'green', label='Precision (Player)', linewidth=2)
    ax3.plot(epochs, recall_ball, 'red', label='Recall (Ball)', linewidth=2, linestyle='--')
    ax3.plot(epochs, recall_player, 'orange', label='Recall (Player)', linewidth=2, linestyle='--')
    ax3.set_xlabel('Epochs')
    ax3.set_ylabel('Score')
    ax3.set_title('Precision & Recall by Class')
    ax3.legend(fontsize=9)
    ax3.grid(True, alpha=0.3)
    ax3.set_ylim(0, 1)
    ax3.set_xlim(0, training_config['epochs'])
    
    # 4. 学习率调度（YOLOv5n默认策略）
    lr_schedule = []
    initial_lr = training_config['lr0']
    warmup_epochs = training_config['warmup_epochs']
    
    for epoch in epochs:
        if epoch <= warmup_epochs:  # 预热阶段
            lr = initial_lr * epoch / warmup_epochs
        elif epoch <= 80:  # 稳定阶段
            lr = initial_lr
        else:  # 衰减阶段
            lr = initial_lr * 0.1
        lr_schedule.append(lr)
    
    ax4.plot(epochs, lr_schedule, 'red', linewidth=3, label='Learning Rate')
    ax4.axvline(x=training_config['close_mosaic'], color='gray', linestyle=':', alpha=0.7, 
                label=f'Close Mosaic (Epoch {training_config["close_mosaic"]})')
    ax4.set_xlabel('Epochs')
    ax4.set_ylabel('Learning Rate')
    ax4.set_title('Learning Rate Schedule')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_yscale('log')
    ax4.set_xlim(0, training_config['epochs'])
    # 确保学习率为正值
    ax4.set_ylim(bottom=1e-4)
    
    plt.tight_layout()
    plt.suptitle('YOLOv5n训练过程', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

# 运行训练过程解释
if __name__ == '__main__':
    simulate_model_info()
    explain_ultralytics_training_process()

import yaml
import os
from pathlib import Path

def create_experiment_configs():
    """创建4个实验的超参数配置文件"""
    print("创建对比实验配置文件\n")
    
    # 基础超参数配置（适用于ultralytics YOLOv5）
    base_config = {
        'lr0': 0.01,          # 初始学习率
        'lrf': 0.01,          # 最终学习率 (lr0 * lrf)
        'momentum': 0.937,     # SGD动量
        'weight_decay': 0.0005, # 权重衰减
        'warmup_epochs': 3.0,  # 预热轮数
        'warmup_momentum': 0.8, # 预热动量
        'warmup_bias_lr': 0.1, # 预热偏置学习率
        'box': 0.05,          # 边界框损失权重
        'cls': 0.5,           # 分类损失权重
        'cls_pw': 1.0,        # 分类正样本权重
        'obj': 1.0,           # 目标性损失权重
        'obj_pw': 1.0,        # 目标性正样本权重
        'iou_t': 0.20,        # IoU训练阈值
        'anchor_t': 4.0,      # anchor倍数阈值
        'fl_gamma': 0.0,      # focal loss gamma
        'hsv_h': 0.015,       # 色调增强
        'hsv_s': 0.7,         # 饱和度增强
        'hsv_v': 0.4,         # 明度增强
        'degrees': 0.0,       # 旋转角度
        'translate': 0.1,     # 平移
        'scale': 0.5,         # 缩放
        'shear': 0.0,         # 剪切
        'perspective': 0.0,   # 透视变换
        'flipud': 0.0,        # 垂直翻转概率
        'fliplr': 0.5,        # 水平翻转概率
        'mosaic': 1.0,        # Mosaic增强概率
        'mixup': 0.0,         # MixUp增强概率
        'copy_paste': 0.0     # Copy-Paste增强概率
    }
    
    # 实验配置
    experiments = {
        'exp1_baseline': {
            'name': '实验一：基线实验（完整数据增强）',
            'description': '使用YOLOv5默认配置，开启所有数据增强',
            'config': base_config.copy()
        },
        'exp2_no_aug': {
            'name': '实验二：无数据增强',
            'description': '关闭所有数据增强功能',
            'config': {**base_config, 
                      'hsv_h': 0.0, 'hsv_s': 0.0, 'hsv_v': 0.0,
                      'degrees': 0.0, 'translate': 0.0, 'scale': 0.0,
                      'shear': 0.0, 'perspective': 0.0,
                      'flipud': 0.0, 'fliplr': 0.0,
                      'mosaic': 0.0, 'mixup': 0.0, 'copy_paste': 0.0}
        },
        'exp3_basic_aug': {
            'name': '实验三：仅常规增强',
            'description': '关闭Mosaic，保留颜色空间和几何变换',
            'config': {**base_config, 'mosaic': 0.0}
        },
        'exp4_mosaic_compare': {
            'name': '实验四：Mosaic对比',
            'description': '在实验三基础上重新开启Mosaic',
            'config': base_config.copy()  # 与基线相同
        }
    }
    
    # 创建配置文件
    config_dir = 'experiment_configs'
    os.makedirs(config_dir, exist_ok=True)
    
    created_files = []
    
    for exp_id, exp_info in experiments.items():
        config_file = f"{config_dir}/{exp_id}.yaml"
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                # 添加注释
                f.write(f"# {exp_info['name']}\n")
                f.write(f"# {exp_info['description']}\n\n")
                
                # 写入配置
                yaml.dump(exp_info['config'], f, default_flow_style=False)
            
            created_files.append(config_file)
            print(f"创建配置文件: {config_file}")
            
        except Exception as e:
            print(f"创建配置文件失败 {config_file}: {e}")
            return False
    
    # 显示实验对比表
    print("\n实验配置对比表:")
    print("-" * 80)
    print(f"{'实验':<12} {'Mosaic':<8} {'颜色增强':<8} {'几何变换':<8} {'翻转':<8} {'目的':<20}")
    print("-" * 80)
    
    comparisons = [
        ('实验一', '✓', '✓', '✓', '✓', '建立性能基准'),
        ('实验二', '❌', '❌', '❌', '❌', '验证增强必要性'),
        ('实验三', '❌', '✓', '✓', '✓', '评估常规增强'),
        ('实验四', '✓', '✓', '✓', '✓', '验证Mosaic价值')
    ]
    
    for exp, mosaic, color, geo, flip, purpose in comparisons:
        print(f"{exp:<12} {mosaic:<8} {color:<8} {geo:<8} {flip:<8} {purpose:<20}")
    
    print("-" * 80)
    
    print(f"\n配置文件已保存到: {config_dir}/")
    print("接下来可以使用这些配置文件进行对比训练")
    
    return True

# 创建实验配置
configs_created = create_experiment_configs()

import os
from ultralytics import YOLO
import torch

def generate_training_commands():
    """生成4个实验的训练配置（使用ultralytics YOLO）"""
    print("生成实验训练配置\n")
    
    # 检查必要文件
    required_files = {
        'e:\\code\\Mo\\yolov5\\dataset\\data.yaml': '数据集配置文件',
        'yolov5n.pt': '预训练权重文件（将自动下载）'
    }
    
    missing_files = []
    for file, desc in required_files.items():
        if file.endswith('.pt'):  # 权重文件会自动下载
            continue
        if not os.path.exists(file):
            missing_files.append(f"{file} ({desc})")
    
    if missing_files:
        print("缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("请先完成前面的步骤")
        return False
    
    # 基础训练参数
    base_params = {
        'data': 'e:\\code\\Mo\\yolov5\\dataset\\data.yaml',  # 数据集配置
        'epochs': 50,                     # 训练轮数
        'batch': 16,                      # 批次大小
        'imgsz': 640,                     # 图片尺寸
        'device': '0' if torch.cuda.is_available() else 'cpu',  # 设备
        'workers': 4,                     # 数据加载进程数
        'cache': True,                    # 缓存数据
        'save_period': 10,                # 每10轮保存一次
        'project': 'runs/train',          # 项目目录
        'exist_ok': True                  # 允许覆盖现有结果
    }
    
    # 实验配置
    experiments = [
        {
            'id': 'exp1_baseline',
            'name': '实验一：基线实验',
            'cfg': 'experiment_configs/exp1_baseline.yaml',
            'name_suffix': 'baseline'
        },
        {
            'id': 'exp2_no_aug',
            'name': '实验二：无数据增强',
            'cfg': 'experiment_configs/exp2_no_aug.yaml',
            'name_suffix': 'no_aug'
        },
        {
            'id': 'exp3_basic_aug',
            'name': '实验三：仅常规增强',
            'cfg': 'experiment_configs/exp3_basic_aug.yaml',
            'name_suffix': 'basic_aug'
        },
        {
            'id': 'exp4_mosaic_compare',
            'name': '实验四：Mosaic对比',
            'cfg': 'experiment_configs/exp4_mosaic_compare.yaml',
            'name_suffix': 'mosaic_compare'
        }
    ]
    
    print("训练配置生成:")
    print("=" * 100)
    
    training_configs = []
    
    for exp in experiments:
        # 构建训练配置
        train_config = base_params.copy()
        train_config['name'] = exp['name_suffix']
        train_config['cfg'] = exp['cfg']
        
        training_configs.append({
            'exp_id': exp['id'],
            'name': exp['name'],
            'config': train_config
        })
        
        print(f"\n{exp['name']}")
        print(f"输出目录: {train_config['project']}/{train_config['name']}")
        print(f"超参数配置: {exp['cfg']}")
        print(f"数据集: {train_config['data']}")
        print("-" * 80)
    
    # 参数详解
    print("\n训练参数详解:")
    param_explanations = {
        'data': '数据集配置文件路径',
        'epochs': '训练轮数（完整遍历数据集的次数）',
        'batch': '批次大小（每次训练的图片数量）',
        'imgsz': '输入图片尺寸（像素）',
        'device': '训练设备（0=GPU, cpu=CPU）',
        'workers': '数据加载进程数',
        'cfg': '超参数配置文件路径',
        'project': '项目输出根目录',
        'name': '实验名称（子目录）',
        'cache': '数据缓存（True=启用）',
        'save_period': '模型保存间隔（轮数）'
    }
    
    for param, desc in param_explanations.items():
        print(f"   {param:<15}: {desc}")
    
    # 训练时间估算
    print("\n训练时间估算:")
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        print(f"   GPU: {gpu_name} ({gpu_memory:.1f}GB)")
        
        if 'RTX' in gpu_name or 'GTX 1660' in gpu_name:
            time_estimate = "约15-25分钟/实验"
        elif 'GTX' in gpu_name:
            time_estimate = "约25-40分钟/实验"
        else:
            time_estimate = "约10-20分钟/实验"
        
        print(f"   预计训练时间: {time_estimate}")
        print(f"   总计4个实验: 约1-2小时")
    else:
        print("   CPU训练: 约2-4小时/实验（不推荐）")
    
    print("\n训练建议:")
    print("   1. 建议按顺序执行实验，便于对比分析")
    print("   2. 训练过程中可以通过TensorBoard监控进度")
    print("   3. 如果显存不足，可以减小batch size")
    print("   4. 训练完成后注意保存和分析结果")
    
    return training_configs

# 生成训练配置
training_configs = generate_training_commands()

import os
import subprocess
from pathlib import Path

def setup_training_monitoring():
    """设置训练监控工具"""
    print("训练监控设置指南\n")
    
    print("1. TensorBoard监控:")
    print("   ultralytics YOLO自动集成TensorBoard，训练时会自动记录指标")
    print("   ") 
    print("   启动TensorBoard命令:")
    print("   ```bash")
    print("   tensorboard --logdir runs/train")
    print("   ```")
    print("   ")
    print("   然后在浏览器中访问: http://localhost:6006")
    
    print("\n2. 监控指标说明:")
    
    metrics_info = {
        'train/box_loss': '边界框回归损失（训练集）',
        'train/cls_loss': '分类损失（训练集）',
        'train/dfl_loss': 'DFL损失（训练集）',
        'val/box_loss': '边界框回归损失（验证集）',
        'val/cls_loss': '分类损失（验证集）',
        'val/dfl_loss': 'DFL损失（验证集）',
        'metrics/precision(B)': '精确率',
        'metrics/recall(B)': '召回率',
        'metrics/mAP50(B)': 'mAP@IoU=0.5',
        'metrics/mAP50-95(B)': 'mAP@IoU=0.5:0.95',
        'lr/pg0': '学习率（参数组0）',
        'lr/pg1': '学习率（参数组1）',
        'lr/pg2': '学习率（参数组2）'
    }
    
    for metric, desc in metrics_info.items():
        print(f"   {metric:<25}: {desc}")
    
    print("\n3. 异常情况识别:")
    
    warning_signs = [
        ("损失不下降", "学习率过高/过低，数据问题，模型配置错误"),
        ("损失震荡剧烈", "学习率过高，batch size过小"),
        ("验证损失上升", "过拟合，需要更强的正则化"),
        ("mAP不提升", "数据质量问题，超参数需要调整"),
        ("GPU利用率低", "数据加载瓶颈，增加workers数量"),
        ("内存不足", "减小batch size或图片尺寸")
    ]
    
    for problem, solution in warning_signs:
        print(f"   {problem:<12}: {solution}")
    
    print("\n4. 监控最佳实践:")
    print("   - 训练开始后立即启动TensorBoard")
    print("   - 每10-20轮检查一次训练曲线")
    print("   - 重点关注验证集指标的变化趋势")
    print("   - 保存关键时刻的模型检查点")
    print("   - 记录异常情况和对应的解决方案")
    
    # 创建监控脚本
    monitoring_script = '''@echo off
echo 启动YOLOv5训练监控

REM 检查TensorBoard是否已安装
pip show tensorboard >nul 2>&1
if errorlevel 1 (
    echo TensorBoard未安装，正在安装...
    pip install tensorboard
)

REM 启动TensorBoard
echo 启动TensorBoard监控...
tensorboard --logdir runs/train --port 6006 --host 0.0.0.0
'''
    
    # 保存监控脚本
    with open('start_monitoring.bat', 'w', encoding='utf-8') as f:
        f.write(monitoring_script)
    
    print("\n已创建监控脚本: start_monitoring.bat")
    print("   使用方法: 双击运行 start_monitoring.bat")
    
    return True

# 设置训练监控
setup_training_monitoring()

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import glob
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_results_analysis_tool():
    """创建实验结果分析工具"""
    print("实验结果分析工具\n")
    
    analysis_code = '''
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import glob
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def analyze_experiment_results():
    """分析4个实验的结果"""
    print("分析实验结果...\\n")
    
    # 实验信息
    experiments = {
        'baseline': {'name': '实验一：基线实验', 'color': 'blue'},
        'no_aug': {'name': '实验二：无数据增强', 'color': 'red'},
        'basic_aug': {'name': '实验三：仅常规增强', 'color': 'green'},
        'mosaic_compare': {'name': '实验四：Mosaic对比', 'color': 'orange'}
    }
    
    results = {}
    
    # 读取每个实验的结果
    for exp_id, exp_info in experiments.items():
        result_path = f'runs/train/{exp_id}/results.csv'
        
        if os.path.exists(result_path):
            try:
                df = pd.read_csv(result_path)
                # 去除列名中的空格并标准化列名
                df.columns = df.columns.str.strip()
                results[exp_id] = df
                print(f"读取结果: {exp_info['name']}")
            except Exception as e:
                print(f"读取失败 {exp_info['name']}: {e}")
        else:
            print(f"结果文件不存在: {result_path}")
    
    if not results:
        print("没有找到任何实验结果")
        return
    
    # 创建对比图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 训练损失对比
    ax1 = axes[0, 0]
    for exp_id, df in results.items():
        # ultralytics使用不同的列名
        loss_cols = ['train/box_loss', 'box_loss', 'train_box_loss']
        loss_col = None
        for col in loss_cols:
            if col in df.columns:
                loss_col = col
                break
        
        if loss_col:
            ax1.plot(df['epoch'], df[loss_col], 
                    label=experiments[exp_id]['name'], 
                    color=experiments[exp_id]['color'])
    ax1.set_xlabel('训练轮数')
    ax1.set_ylabel('训练损失')
    ax1.set_title('训练损失对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 验证损失对比
    ax2 = axes[0, 1]
    for exp_id, df in results.items():
        val_loss_cols = ['val/box_loss', 'val_box_loss', 'box_loss']
        val_loss_col = None
        for col in val_loss_cols:
            if col in df.columns:
                val_loss_col = col
                break
        
        if val_loss_col:
            ax2.plot(df['epoch'], df[val_loss_col], 
                    label=experiments[exp_id]['name'], 
                    color=experiments[exp_id]['color'])
    ax2.set_xlabel('训练轮数')
    ax2.set_ylabel('验证损失')
    ax2.set_title('验证损失对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. mAP对比
    ax3 = axes[1, 0]
    for exp_id, df in results.items():
        map_cols = ['metrics/mAP50(B)', 'mAP_0.5', 'mAP50']
        map_col = None
        for col in map_cols:
            if col in df.columns:
                map_col = col
                break
        
        if map_col:
            ax3.plot(df['epoch'], df[map_col], 
                    label=experiments[exp_id]['name'], 
                    color=experiments[exp_id]['color'])
    ax3.set_xlabel('训练轮数')
    ax3.set_ylabel('mAP@0.5')
    ax3.set_title('mAP@0.5对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 精确率和召回率对比
    ax4 = axes[1, 1]
    for exp_id, df in results.items():
        precision_cols = ['metrics/precision(B)', 'precision', 'P']
        recall_cols = ['metrics/recall(B)', 'recall', 'R']
        
        precision_col = None
        recall_col = None
        
        for col in precision_cols:
            if col in df.columns:
                precision_col = col
                break
        
        for col in recall_cols:
            if col in df.columns:
                recall_col = col
                break
        
        if precision_col and recall_col:
            # 只显示最终的精确率和召回率
            final_precision = df[precision_col].iloc[-1]
            final_recall = df[recall_col].iloc[-1]
            ax4.scatter(final_recall, final_precision, 
                       label=experiments[exp_id]['name'], 
                       color=experiments[exp_id]['color'], s=100)
    ax4.set_xlabel('召回率 (Recall)')
    ax4.set_ylabel('精确率 (Precision)')
    ax4.set_title('精确率 vs 召回率')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.suptitle('四个实验结果对比分析', fontsize=16, fontweight='bold', y=1.02)
    plt.show()
    
    # 生成结果总结表
    print("\\n实验结果总结:")
    print("-" * 80)
    print(f"{'实验':<20} {'最终mAP@0.5':<12} {'精确率':<8} {'召回率':<8} {'训练损失':<10}")
    print("-" * 80)
    
    for exp_id, df in results.items():
        exp_name = experiments[exp_id]['name']
        
        # 查找mAP列
        map_cols = ['metrics/mAP50(B)', 'mAP_0.5', 'mAP50']
        map_col = next((col for col in map_cols if col in df.columns), None)
        final_map = df[map_col].iloc[-1] if map_col else 'N/A'
        
        # 查找精确率列
        precision_cols = ['metrics/precision(B)', 'precision', 'P']
        precision_col = next((col for col in precision_cols if col in df.columns), None)
        final_precision = df[precision_col].iloc[-1] if precision_col else 'N/A'
        
        # 查找召回率列
        recall_cols = ['metrics/recall(B)', 'recall', 'R']
        recall_col = next((col for col in recall_cols if col in df.columns), None)
        final_recall = df[recall_col].iloc[-1] if recall_col else 'N/A'
        
        # 查找训练损失列
        loss_cols = ['train/box_loss', 'box_loss', 'train_box_loss']
        loss_col = next((col for col in loss_cols if col in df.columns), None)
        final_loss = df[loss_col].iloc[-1] if loss_col else 'N/A'
        
        if isinstance(final_map, (int, float)):
            final_map = f"{final_map:.3f}"
        if isinstance(final_precision, (int, float)):
            final_precision = f"{final_precision:.3f}"
        if isinstance(final_recall, (int, float)):
            final_recall = f"{final_recall:.3f}"
        if isinstance(final_loss, (int, float)):
            final_loss = f"{final_loss:.3f}"
        
        print(f"{exp_name:<20} {final_map:<12} {final_precision:<8} {final_recall:<8} {final_loss:<10}")
    
    print("-" * 80)
    
    # 分析结论
    print("\\n分析结论:")
    print("1. 数据增强的重要性:")
    print("   - 对比实验一和实验二，观察数据增强对性能的整体影响")
    print("2. 常规增强的效果:")
    print("   - 对比实验二和实验三，评估传统数据增强的贡献")
    print("3. Mosaic增强的价值:")
    print("   - 对比实验三和实验四，验证Mosaic的独特作用")
    print("4. 过拟合情况:")
    print("   - 观察训练损失和验证损失的差异，判断过拟合程度")

# 运行分析
analyze_experiment_results()
'''
    
    # 保存分析代码
    with open('analyze_results.py', 'w', encoding='utf-8') as f:
        f.write(analysis_code)
    
    print("已创建结果分析工具: analyze_results.py")
    print("\n使用方法:")
    print("   1. 等待所有实验训练完成")
    print("   2. 运行: python analyze_results.py")
    print("   3. 查看生成的对比图表和结果总结")
    
    print("\n分析要点:")
    print("   - 重点关注mAP@0.5指标的变化")
    print("   - 观察训练损失和验证损失的收敛情况")
    print("   - 分析精确率和召回率的平衡")
    print("   - 评估不同数据增强策略的效果")
    
    return True

# 创建结果分析工具
create_results_analysis_tool()

def explain_evaluation_metrics():
    """详细解释目标检测评估指标"""
    print("目标检测评估指标详解\n")
    
    # 创建指标解释可视化
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 混淆矩阵概念图
    confusion_data = np.array([[85, 15], [10, 90]])
    im1 = ax1.imshow(confusion_data, cmap='Blues', alpha=0.8)
    
    # 添加数值标签
    for i in range(2):
        for j in range(2):
            ax1.text(j, i, confusion_data[i, j], ha='center', va='center', 
                    fontsize=20, fontweight='bold')
    
    ax1.set_xticks([0, 1])
    ax1.set_yticks([0, 1])
    ax1.set_xticklabels(['预测负例', '预测正例'])
    ax1.set_yticklabels(['实际负例', '实际正例'])
    ax1.set_xlabel('预测结果')
    ax1.set_ylabel('实际结果')
    ax1.set_title('混淆矩阵示例\n(TN=85, FP=15, FN=10, TP=90)')
    
    # 添加标签说明
    ax1.text(0, 0, 'TN\n(真负例)', ha='center', va='bottom', 
            fontsize=10, color='white', fontweight='bold')
    ax1.text(1, 0, 'FP\n(假正例)', ha='center', va='bottom', 
            fontsize=10, color='white', fontweight='bold')
    ax1.text(0, 1, 'FN\n(假负例)', ha='center', va='bottom', 
            fontsize=10, color='white', fontweight='bold')
    ax1.text(1, 1, 'TP\n(真正例)', ha='center', va='bottom', 
            fontsize=10, color='white', fontweight='bold')
    
    # 2. PR曲线示例
    # 模拟不同模型的PR曲线
    recall = np.linspace(0, 1, 100)
    
    # 优秀模型：高精确率，高召回率
    precision_good = 0.95 - 0.3 * recall**2
    precision_good = np.clip(precision_good, 0.6, 1.0)
    
    # 一般模型：中等性能
    precision_avg = 0.8 - 0.5 * recall
    precision_avg = np.clip(precision_avg, 0.3, 1.0)
    
    # 较差模型：低精确率
    precision_poor = 0.6 - 0.4 * recall
    precision_poor = np.clip(precision_poor, 0.2, 1.0)
    
    ax2.plot(recall, precision_good, 'g-', linewidth=3, label='优秀模型 (AP=0.85)')
    ax2.plot(recall, precision_avg, 'b-', linewidth=3, label='一般模型 (AP=0.65)')
    ax2.plot(recall, precision_poor, 'r-', linewidth=3, label='较差模型 (AP=0.45)')
    
    ax2.set_xlabel('召回率 (Recall)')
    ax2.set_ylabel('精确率 (Precision)')
    ax2.set_title('PR曲线对比\n(曲线下面积 = Average Precision)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(0, 1)
    ax2.set_ylim(0, 1)
    
    # 3. IoU阈值对mAP的影响
    iou_thresholds = np.arange(0.5, 1.0, 0.05)
    map_values = 0.8 * np.exp(-2 * (iou_thresholds - 0.5))
    
    ax3.plot(iou_thresholds, map_values, 'purple', linewidth=3, marker='o')
    ax3.axvline(x=0.5, color='red', linestyle='--', alpha=0.7, label='mAP@0.5')
    ax3.axvline(x=0.75, color='orange', linestyle='--', alpha=0.7, label='mAP@0.75')
    
    ax3.set_xlabel('IoU阈值')
    ax3.set_ylabel('mAP值')
    ax3.set_title('IoU阈值对mAP的影响\n(阈值越高，要求越严格)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 精确率vs召回率权衡
    # 模拟不同置信度阈值下的精确率和召回率
    confidence_thresholds = np.linspace(0.1, 0.9, 20)
    precision_curve = 0.5 + 0.4 * confidence_thresholds
    recall_curve = 0.95 - 0.7 * confidence_thresholds
    
    ax4.plot(confidence_thresholds, precision_curve, 'blue', linewidth=3, 
            marker='s', label='精确率', markersize=6)
    ax4.plot(confidence_thresholds, recall_curve, 'red', linewidth=3, 
            marker='o', label='召回率', markersize=6)
    
    # 找到F1最大值点
    f1_scores = 2 * (precision_curve * recall_curve) / (precision_curve + recall_curve)
    best_idx = np.argmax(f1_scores)
    ax4.axvline(x=confidence_thresholds[best_idx], color='green', 
               linestyle='--', alpha=0.7, label=f'最佳F1点 ({confidence_thresholds[best_idx]:.2f})')
    
    ax4.set_xlabel('置信度阈值')
    ax4.set_ylabel('指标值')
    ax4.set_title('精确率与召回率的权衡\n(置信度阈值的影响)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.suptitle('目标检测评估指标全景图', fontsize=16, fontweight='bold', y=1.02)
    plt.show()
    
    # 指标详细解释
    print("核心指标详解:")
    print("\n基础概念:")
    print("   TP (True Positive):  正确检测到的目标")
    print("   FP (False Positive): 错误检测的背景区域")
    print("   FN (False Negative): 漏检的真实目标")
    print("   TN (True Negative):  正确识别的背景区域")
    
    print("\n核心指标:")
    print("   精确率 (Precision) = TP / (TP + FP)")
    print("   召回率 (Recall)    = TP / (TP + FN)")
    print("   F1分数 (F1-Score)  = 2 × (Precision × Recall) / (Precision + Recall)")
    print("   AP (Average Precision): PR曲线下的面积")
    print("   mAP (mean AP): 所有类别AP的平均值")
    
    print("\n业务含义:")
    print("   精确率高 → 检测结果可信度高，误报少")
    print("   召回率高 → 检测覆盖面广，漏检少")
    print("   F1分数高 → 精确率和召回率平衡好")
    print("   mAP高    → 整体检测性能优秀")
    
    print("\n权衡考虑:")
    print("   安全关键应用 → 优先保证召回率（不能漏检）")
    print("   资源受限应用 → 优先保证精确率（减少误报）")
    print("   平衡应用场景 → 关注F1分数和mAP")

# 运行评估指标解释
explain_evaluation_metrics()

import pandas as pd
import seaborn as sns
from sklearn.metrics import confusion_matrix
import json

def comprehensive_results_analysis():
    """对4个实验进行综合结果分析"""
    print("综合实验结果分析\n")
    
    # 实验配置信息
    experiments = {
        'baseline': {
            'name': '实验一：基线实验',
            'color': '#2E86AB',
            'description': '完整数据增强'
        },
        'no_aug': {
            'name': '实验二：无数据增强',
            'color': '#A23B72',
            'description': '关闭所有增强'
        },
        'basic_aug': {
            'name': '实验三：仅常规增强',
            'color': '#F18F01',
            'description': '传统增强技术'
        },
        'mosaic_compare': {
            'name': '实验四：Mosaic对比',
            'color': '#C73E1D',
            'description': '重新开启Mosaic'
        }
    }
    
    # 尝试读取实验结果
    results_data = {}
    available_experiments = []
    
    for exp_id, exp_info in experiments.items():
        result_path = f'yolov5/runs/train/{exp_id}/results.csv'
        
        if os.path.exists(result_path):
            try:
                df = pd.read_csv(result_path)
                df.columns = df.columns.str.strip()  # 清理列名
                results_data[exp_id] = df
                available_experiments.append(exp_id)
                print(f"成功读取: {exp_info['name']}")
            except Exception as e:
                print(f"读取失败 {exp_info['name']}: {e}")
        else:
            print(f" 结果文件不存在: {result_path}")
    
    if len(available_experiments) == 0:
        print("\n没有找到任何实验结果文件")
        print("请确保已完成训练并且结果文件位于正确路径")
        
        # 创建模拟数据用于演示
        print("\n使用模拟数据进行演示分析...")
        results_data = create_mock_results()
        available_experiments = list(results_data.keys())
    
    # 创建综合分析图表
    create_comprehensive_analysis_plots(results_data, experiments, available_experiments)
    
    # 生成性能对比表
    generate_performance_comparison_table(results_data, experiments, available_experiments)
    
    return results_data, available_experiments

def create_mock_results():
    """创建模拟实验结果用于演示"""
    epochs = list(range(1, 51))  # 50个epoch
    
    mock_data = {
        'baseline': pd.DataFrame({
            'epoch': epochs,
            'train/box_loss': [3.0 * np.exp(-i/15) + 0.3 + 0.05*np.random.randn() for i in epochs],
            'val/box_loss': [3.2 * np.exp(-i/18) + 0.35 + 0.08*np.random.randn() for i in epochs],
            'metrics/precision': [0.75 * (1 - np.exp(-i/12)) + 0.02*np.random.randn() for i in epochs],
            'metrics/recall': [0.78 * (1 - np.exp(-i/14)) + 0.02*np.random.randn() for i in epochs],
            'metrics/mAP_0.5': [0.72 * (1 - np.exp(-i/16)) + 0.03*np.random.randn() for i in epochs]
        }),
        'no_aug': pd.DataFrame({
            'epoch': epochs,
            'train/box_loss': [3.5 * np.exp(-i/10) + 0.2 + 0.05*np.random.randn() for i in epochs],
            'val/box_loss': [4.0 * np.exp(-i/8) + 0.8 + 0.1*np.random.randn() for i in epochs],
            'metrics/precision': [0.55 * (1 - np.exp(-i/8)) + 0.03*np.random.randn() for i in epochs],
            'metrics/recall': [0.48 * (1 - np.exp(-i/10)) + 0.03*np.random.randn() for i in epochs],
            'metrics/mAP_0.5': [0.45 * (1 - np.exp(-i/12)) + 0.04*np.random.randn() for i in epochs]
        }),
        'basic_aug': pd.DataFrame({
            'epoch': epochs,
            'train/box_loss': [3.2 * np.exp(-i/13) + 0.25 + 0.05*np.random.randn() for i in epochs],
            'val/box_loss': [3.6 * np.exp(-i/15) + 0.45 + 0.08*np.random.randn() for i in epochs],
            'metrics/precision': [0.68 * (1 - np.exp(-i/11)) + 0.02*np.random.randn() for i in epochs],
            'metrics/recall': [0.65 * (1 - np.exp(-i/12)) + 0.02*np.random.randn() for i in epochs],
            'metrics/mAP_0.5': [0.62 * (1 - np.exp(-i/14)) + 0.03*np.random.randn() for i in epochs]
        }),
        'mosaic_compare': pd.DataFrame({
            'epoch': epochs,
            'train/box_loss': [3.1 * np.exp(-i/14) + 0.28 + 0.05*np.random.randn() for i in epochs],
            'val/box_loss': [3.3 * np.exp(-i/17) + 0.38 + 0.08*np.random.randn() for i in epochs],
            'metrics/precision': [0.73 * (1 - np.exp(-i/11)) + 0.02*np.random.randn() for i in epochs],
            'metrics/recall': [0.76 * (1 - np.exp(-i/13)) + 0.02*np.random.randn() for i in epochs],
            'metrics/mAP_0.5': [0.70 * (1 - np.exp(-i/15)) + 0.03*np.random.randn() for i in epochs]
        })
    }
    
    # 确保数值在合理范围内
    for exp_id, df in mock_data.items():
        df['metrics/precision'] = np.clip(df['metrics/precision'], 0, 1)
        df['metrics/recall'] = np.clip(df['metrics/recall'], 0, 1)
        df['metrics/mAP_0.5'] = np.clip(df['metrics/mAP_0.5'], 0, 1)
        df['train/box_loss'] = np.clip(df['train/box_loss'], 0.1, 5)
        df['val/box_loss'] = np.clip(df['val/box_loss'], 0.1, 5)
    
    return mock_data

def create_comprehensive_analysis_plots(results_data, experiments, available_experiments):
    """创建综合分析图表"""
    if len(available_experiments) == 0:
        return
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 训练损失对比
    ax1 = axes[0, 0]
    for exp_id in available_experiments:
        df = results_data[exp_id]
        if 'train/box_loss' in df.columns:
            ax1.plot(df['epoch'], df['train/box_loss'], 
                    label=experiments[exp_id]['description'], 
                    color=experiments[exp_id]['color'], linewidth=2)
    ax1.set_xlabel('训练轮数')
    ax1.set_ylabel('训练损失')
    ax1.set_title('训练损失收敛对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 验证损失对比
    ax2 = axes[0, 1]
    for exp_id in available_experiments:
        df = results_data[exp_id]
        if 'val/box_loss' in df.columns:
            ax2.plot(df['epoch'], df['val/box_loss'], 
                    label=experiments[exp_id]['description'], 
                    color=experiments[exp_id]['color'], linewidth=2)
    ax2.set_xlabel('训练轮数')
    ax2.set_ylabel('验证损失')
    ax2.set_title('验证损失变化对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. mAP对比
    ax3 = axes[0, 2]
    for exp_id in available_experiments:
        df = results_data[exp_id]
        if 'metrics/mAP_0.5' in df.columns:
            ax3.plot(df['epoch'], df['metrics/mAP_0.5'], 
                    label=experiments[exp_id]['description'], 
                    color=experiments[exp_id]['color'], linewidth=2)
    ax3.set_xlabel('训练轮数')
    ax3.set_ylabel('mAP@0.5')
    ax3.set_title('mAP@0.5性能对比')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 精确率对比
    ax4 = axes[1, 0]
    for exp_id in available_experiments:
        df = results_data[exp_id]
        if 'metrics/precision' in df.columns:
            ax4.plot(df['epoch'], df['metrics/precision'], 
                    label=experiments[exp_id]['description'], 
                    color=experiments[exp_id]['color'], linewidth=2)
    ax4.set_xlabel('训练轮数')
    ax4.set_ylabel('精确率')
    ax4.set_title('精确率变化对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # 5. 召回率对比
    ax5 = axes[1, 1]
    for exp_id in available_experiments:
        df = results_data[exp_id]
        if 'metrics/recall' in df.columns:
            ax5.plot(df['epoch'], df['metrics/recall'], 
                    label=experiments[exp_id]['description'], 
                    color=experiments[exp_id]['color'], linewidth=2)
    ax5.set_xlabel('训练轮数')
    ax5.set_ylabel('召回率')
    ax5.set_title('召回率变化对比')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # 6. 过拟合分析（训练损失vs验证损失差异）
    ax6 = axes[1, 2]
    for exp_id in available_experiments:
        df = results_data[exp_id]
        if 'train/box_loss' in df.columns and 'val/box_loss' in df.columns:
            gap = df['val/box_loss'] - df['train/box_loss']
            ax6.plot(df['epoch'], gap, 
                    label=experiments[exp_id]['description'], 
                    color=experiments[exp_id]['color'], linewidth=2)
    ax6.set_xlabel('训练轮数')
    ax6.set_ylabel('验证损失 - 训练损失')
    ax6.set_title(' 过拟合程度分析\n(差值越大，过拟合越严重)')
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    ax6.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.suptitle('四个实验的综合性能分析', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

# 执行综合分析
results_data, available_experiments = comprehensive_results_analysis()

def generate_performance_comparison_table(results_data, experiments, available_experiments):
    """生成性能对比表"""
    if len(available_experiments) == 0:
        return
    
    print("\n实验性能对比总结表")
    print("=" * 100)
    
    # 表头
    header = f"{'实验':<20} {'最终mAP@0.5':<12} {'精确率':<10} {'召回率':<10} {'F1分数':<10} {'训练损失':<10} {'验证损失':<10} {'过拟合程度':<12}"
    print(header)
    print("=" * 100)
    
    # 收集所有实验的最终指标
    comparison_data = []
    
    for exp_id in available_experiments:
        df = results_data[exp_id]
        exp_name = experiments[exp_id]['description']
        
        # 获取最后一轮的指标
        final_map = df['metrics/mAP_0.5'].iloc[-1] if 'metrics/mAP_0.5' in df.columns else 0
        final_precision = df['metrics/precision'].iloc[-1] if 'metrics/precision' in df.columns else 0
        final_recall = df['metrics/recall'].iloc[-1] if 'metrics/recall' in df.columns else 0
        final_train_loss = df['train/box_loss'].iloc[-1] if 'train/box_loss' in df.columns else 0
        final_val_loss = df['val/box_loss'].iloc[-1] if 'val/box_loss' in df.columns else 0
        
        # 计算F1分数
        if final_precision > 0 and final_recall > 0:
            f1_score = 2 * (final_precision * final_recall) / (final_precision + final_recall)
        else:
            f1_score = 0
        
        # 计算过拟合程度（验证损失与训练损失的差值）
        overfitting_degree = final_val_loss - final_train_loss
        
        # 格式化输出
        row = f"{exp_name:<20} {final_map:<12.3f} {final_precision:<10.3f} {final_recall:<10.3f} {f1_score:<10.3f} {final_train_loss:<10.3f} {final_val_loss:<10.3f} {overfitting_degree:<12.3f}"
        print(row)
        
        # 保存数据用于后续分析
        comparison_data.append({
            'experiment': exp_name,
            'exp_id': exp_id,
            'mAP': final_map,
            'precision': final_precision,
            'recall': final_recall,
            'f1': f1_score,
            'train_loss': final_train_loss,
            'val_loss': final_val_loss,
            'overfitting': overfitting_degree
        })
    
    print("=" * 100)
    
    # 找出最佳表现
    if comparison_data:
        best_map = max(comparison_data, key=lambda x: x['mAP'])
        best_precision = max(comparison_data, key=lambda x: x['precision'])
        best_recall = max(comparison_data, key=lambda x: x['recall'])
        best_f1 = max(comparison_data, key=lambda x: x['f1'])
        least_overfitting = min(comparison_data, key=lambda x: abs(x['overfitting']))
        
        print("\n各项指标最佳表现:")
        print(f"   最高mAP@0.5: {best_map['experiment']} ({best_map['mAP']:.3f})")
        print(f"   最高精确率: {best_precision['experiment']} ({best_precision['precision']:.3f})")
        print(f"   最高召回率: {best_recall['experiment']} ({best_recall['recall']:.3f})")
        print(f"   最高F1分数: {best_f1['experiment']} ({best_f1['f1']:.3f})")
        print(f"    最少过拟合: {least_overfitting['experiment']} ({least_overfitting['overfitting']:.3f})")
    
    # 创建雷达图对比
    create_radar_chart(comparison_data, experiments)
    
    return comparison_data

def create_radar_chart(comparison_data, experiments):
    """创建雷达图对比不同实验"""
    if len(comparison_data) == 0:
        return
    
    # 准备雷达图数据
    categories = ['mAP@0.5', '精确率', '召回率', 'F1分数', '抗过拟合']
    
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # 设置角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    # 为每个实验绘制雷达图
    for data in comparison_data:
        # 标准化数据到0-1范围
        values = [
            data['mAP'],
            data['precision'],
            data['recall'],
            data['f1'],
            max(0, 1 - abs(data['overfitting']))  # 过拟合程度转换为抗过拟合能力
        ]
        values += values[:1]  # 闭合图形
        
        # 获取颜色
        color = experiments[data['exp_id']]['color']
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2, label=data['experiment'], color=color)
        ax.fill(angles, values, alpha=0.25, color=color)
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    ax.set_title('🕸️ 四个实验的综合性能雷达图', size=16, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
    ax.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("\n雷达图解读:")
    print("   - 图形面积越大，综合性能越好")
    print("   - 各个维度的平衡性体现模型的稳定性")
    print("   - 突出的尖角表示该维度的特殊优势")
    print("   - 凹陷的部分表示需要改进的方向")

# 生成性能对比表（如果有数据的话）
if 'results_data' in locals() and 'available_experiments' in locals():
    comparison_data = generate_performance_comparison_table(results_data, {
        'baseline': {'name': '实验一：基线实验', 'color': '#2E86AB', 'description': '完整数据增强'},
        'no_aug': {'name': '实验二：无数据增强', 'color': '#A23B72', 'description': '关闭所有增强'},
        'basic_aug': {'name': '实验三：仅常规增强', 'color': '#F18F01', 'description': '传统增强技术'},
        'mosaic_compare': {'name': '实验四：Mosaic对比', 'color': '#C73E1D', 'description': '重新开启Mosaic'}
    }, available_experiments)
else:
    print(" 没有可用的实验数据，跳过性能对比表生成")

import torch
import cv2
import numpy as np
from pathlib import Path
import time
import glob
from PIL import Image, ImageDraw, ImageFont
import matplotlib.patches as patches

def setup_inference_environment():
    """设置推理环境和检查模型文件"""
    print("设置模型推理环境\n")
    
    # 检查YOLOv5环境
    if not os.path.exists('yolov5'):
        print("YOLOv5目录不存在，请先完成第一步的环境搭建")
        return False
    
    # 查找训练好的模型权重
    model_paths = {
        'baseline': 'yolov5/runs/train/baseline/weights/best.pt',
        'no_aug': 'yolov5/runs/train/no_aug/weights/best.pt',
        'basic_aug': 'yolov5/runs/train/basic_aug/weights/best.pt',
        'mosaic_compare': 'yolov5/runs/train/mosaic_compare/weights/best.pt'
    }
    
    available_models = {}
    
    print("检查训练好的模型权重:")
    for exp_name, model_path in model_paths.items():
        if os.path.exists(model_path):
            # 获取文件大小
            file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
            available_models[exp_name] = model_path
            print(f"   {exp_name}: {model_path} ({file_size:.1f}MB)")
        else:
            print(f"   {exp_name}: {model_path} (不存在)")
    
    if not available_models:
        print("\n 没有找到训练好的模型权重")
        print("请先完成第六步的模型训练，或使用预训练权重进行演示")
        
        # 提供预训练权重作为备选
        if os.path.exists('yolov5n.pt'):
            available_models['pretrained'] = 'yolov5n.pt'
            print(f"   使用预训练权重: yolov5n.pt")
        else:
            print("   也没有找到预训练权重文件")
            return False
    
    # 检查测试图片
    test_image_paths = []
    possible_test_dirs = ['dataset/images/val', 'dataset/images/test', 'test_images']
    
    print("\n检查测试图片:")
    for test_dir in possible_test_dirs:
        if os.path.exists(test_dir):
            images = glob.glob(f"{test_dir}/*.jpg") + glob.glob(f"{test_dir}/*.png")
            if images:
                test_image_paths.extend(images[:5])  # 最多取5张
                print(f"   找到 {len(images)} 张图片在 {test_dir}")
                break
    
    if not test_image_paths:
        print("    没有找到测试图片")
        print("   请在dataset/images/val目录下放置一些测试图片")
        return False
    
    print(f"\n推理环境检查完成")
    print(f"   - 可用模型: {len(available_models)} 个")
    print(f"   - 测试图片: {len(test_image_paths)} 张")
    
    return {
        'models': available_models,
        'test_images': test_image_paths
    }

def load_yolov5_model(model_path, device='auto'):
    """加载YOLOv5模型"""
    try:
        # 自动选择设备
        if device == 'auto':
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        print(f"加载模型: {model_path}")
        print(f" 使用设备: {device}")
        
        # 加载模型
        model = torch.hub.load('yolov5', 'custom', path=model_path, source='local', device=device)
        
        # 设置推理参数
        model.conf = 0.25  # 置信度阈值
        model.iou = 0.45   # NMS IoU阈值
        model.max_det = 1000  # 最大检测数量
        
        print(f"模型加载成功")
        print(f"   - 类别数量: {len(model.names)}")
        print(f"   - 类别名称: {list(model.names.values())}")
        
        return model
        
    except Exception as e:
        print(f"模型加载失败: {e}")
        return None

# 设置推理环境
inference_setup = setup_inference_environment()

def perform_single_image_inference(model_path, image_path, conf_threshold=0.25, iou_threshold=0.45):
    """对单张图片进行推理"""
    print(f"单张图片推理演示\n")
    print(f"图片路径: {image_path}")
    print(f"模型路径: {model_path}")
    print(f"  置信度阈值: {conf_threshold}")
    print(f"  NMS阈值: {iou_threshold}")
    
    # 加载模型
    model = load_yolov5_model(model_path)
    if model is None:
        return None
    
    # 设置推理参数
    model.conf = conf_threshold
    model.iou = iou_threshold
    
    try:
        # 记录推理时间
        start_time = time.time()
        
        # 执行推理
        print(f"\n执行推理...")
        results = model(image_path)
        
        inference_time = time.time() - start_time
        print(f" 推理耗时: {inference_time:.3f} 秒")
        
        # 解析结果
        detections = results.pandas().xyxy[0]  # 获取检测结果
        print(f"检测到 {len(detections)} 个目标")
        
        if len(detections) > 0:
            print("\n检测结果详情:")
            for idx, detection in detections.iterrows():
                print(f"   目标 {idx+1}: {detection['name']} (置信度: {detection['confidence']:.3f})")
                print(f"           位置: ({detection['xmin']:.0f}, {detection['ymin']:.0f}) - ({detection['xmax']:.0f}, {detection['ymax']:.0f})")
        
        # 可视化结果
        visualize_detection_results(image_path, results, model.names)
        
        return {
            'results': results,
            'detections': detections,
            'inference_time': inference_time,
            'model_names': model.names
        }
        
    except Exception as e:
        print(f"推理过程出错: {e}")
        return None

def visualize_detection_results(image_path, results, class_names):
    """可视化检测结果"""
    # 读取原图
    original_img = cv2.imread(image_path)
    original_img = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
    
    # 获取检测结果图片
    result_img = results.render()[0]  # 渲染检测结果
    
    # 创建对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 显示原图
    ax1.imshow(original_img)
    ax1.set_title('原始图片', fontsize=14, fontweight='bold')
    ax1.axis('off')
    
    # 显示检测结果
    ax2.imshow(result_img)
    ax2.set_title('检测结果', fontsize=14, fontweight='bold')
    ax2.axis('off')
    
    plt.tight_layout()
    plt.show()
    
    # 显示详细统计信息
    detections = results.pandas().xyxy[0]
    if len(detections) > 0:
        print("\n检测统计:")
        class_counts = detections['name'].value_counts()
        for class_name, count in class_counts.items():
            print(f"   {class_name}: {count} 个")
        
        avg_confidence = detections['confidence'].mean()
        print(f"\n平均置信度: {avg_confidence:.3f}")
        print(f"最高置信度: {detections['confidence'].max():.3f}")
        print(f"最低置信度: {detections['confidence'].min():.3f}")

# 如果有可用的模型和测试图片，进行单张图片推理演示
if inference_setup and len(inference_setup['models']) > 0 and len(inference_setup['test_images']) > 0:
    # 选择第一个可用模型和第一张测试图片
    first_model = list(inference_setup['models'].values())[0]
    first_image = inference_setup['test_images'][0]
    
    print("开始单张图片推理演示...")
    single_result = perform_single_image_inference(first_model, first_image)
else:
    print(" 跳过单张图片推理演示，缺少必要的模型或测试图片")
    single_result = None

def batch_inference_with_stats(model_path, image_paths, conf_threshold=0.25, iou_threshold=0.45):
    """批量推理并统计性能"""
    print(f"批量推理性能统计\n")
    print(f"模型: {os.path.basename(model_path)}")
    print(f"图片数量: {len(image_paths)}")
    print(f"  置信度阈值: {conf_threshold}")
    print(f"  NMS阈值: {iou_threshold}")
    
    # 加载模型
    model = load_yolov5_model(model_path)
    if model is None:
        return None
    
    model.conf = conf_threshold
    model.iou = iou_threshold
    
    # 统计信息
    inference_times = []
    detection_counts = []
    confidence_scores = []
    all_detections = []
    
    print(f"\n开始批量推理...")
    
    for i, image_path in enumerate(image_paths):
        try:
            # 单张图片推理
            start_time = time.time()
            results = model(image_path)
            inference_time = time.time() - start_time
            
            # 解析结果
            detections = results.pandas().xyxy[0]
            
            # 收集统计信息
            inference_times.append(inference_time)
            detection_counts.append(len(detections))
            
            if len(detections) > 0:
                confidence_scores.extend(detections['confidence'].tolist())
                all_detections.append({
                    'image': os.path.basename(image_path),
                    'detections': detections
                })
            
            print(f"   图片 {i+1}/{len(image_paths)}: {len(detections)} 个目标, {inference_time:.3f}s")
            
        except Exception as e:
            print(f"   图片 {i+1} 推理失败: {e}")
            continue
    
    # 计算统计指标
    if inference_times:
        avg_inference_time = np.mean(inference_times)
        total_detections = sum(detection_counts)
        avg_detections = np.mean(detection_counts)
        fps = 1.0 / avg_inference_time if avg_inference_time > 0 else 0
        
        print(f"\n性能统计结果:")
        print(f"    平均推理时间: {avg_inference_time:.3f} 秒")
        print(f"   推理速度(FPS): {fps:.1f}")
        print(f"   总检测数量: {total_detections}")
        print(f"   平均每张检测: {avg_detections:.1f} 个")
        
        if confidence_scores:
            avg_confidence = np.mean(confidence_scores)
            print(f"   平均置信度: {avg_confidence:.3f}")
        
        # 创建性能可视化
        create_performance_visualization(inference_times, detection_counts, confidence_scores)
        
        return {
            'model_path': model_path,
            'inference_times': inference_times,
            'detection_counts': detection_counts,
            'confidence_scores': confidence_scores,
            'avg_inference_time': avg_inference_time,
            'fps': fps,
            'total_detections': total_detections,
            'all_detections': all_detections
        }
    
    return None

def create_performance_visualization(inference_times, detection_counts, confidence_scores):
    """创建性能可视化图表"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 推理时间分布
    ax1.hist(inference_times, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_xlabel('推理时间 (秒)')
    ax1.set_ylabel('频次')
    ax1.set_title('推理时间分布')
    ax1.grid(True, alpha=0.3)
    
    # 添加统计线
    mean_time = np.mean(inference_times)
    ax1.axvline(mean_time, color='red', linestyle='--', label=f'平均值: {mean_time:.3f}s')
    ax1.legend()
    
    # 2. 检测数量分布
    ax2.hist(detection_counts, bins=max(1, max(detection_counts)+1), alpha=0.7, color='lightgreen', edgecolor='black')
    ax2.set_xlabel('检测数量')
    ax2.set_ylabel('频次')
    ax2.set_title('检测数量分布')
    ax2.grid(True, alpha=0.3)
    
    # 3. 置信度分布
    if confidence_scores:
        ax3.hist(confidence_scores, bins=30, alpha=0.7, color='orange', edgecolor='black')
        ax3.set_xlabel('置信度')
        ax3.set_ylabel('频次')
        ax3.set_title('置信度分布')
        ax3.grid(True, alpha=0.3)
        
        # 添加统计线
        mean_conf = np.mean(confidence_scores)
        ax3.axvline(mean_conf, color='red', linestyle='--', label=f'平均值: {mean_conf:.3f}')
        ax3.legend()
    else:
        ax3.text(0.5, 0.5, '无检测结果', ha='center', va='center', transform=ax3.transAxes)
        ax3.set_title('置信度分布')
    
    # 4. 推理时间趋势
    ax4.plot(range(1, len(inference_times)+1), inference_times, 'b-o', alpha=0.7)
    ax4.set_xlabel('图片序号')
    ax4.set_ylabel('推理时间 (秒)')
    ax4.set_title('推理时间趋势')
    ax4.grid(True, alpha=0.3)
    
    # 添加趋势线
    if len(inference_times) > 1:
        z = np.polyfit(range(len(inference_times)), inference_times, 1)
        p = np.poly1d(z)
        ax4.plot(range(1, len(inference_times)+1), p(range(len(inference_times))), "r--", alpha=0.8, label='趋势线')
        ax4.legend()
    
    plt.tight_layout()
    plt.suptitle('批量推理性能分析', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

# 执行批量推理演示
if inference_setup and len(inference_setup['models']) > 0 and len(inference_setup['test_images']) > 0:
    first_model = list(inference_setup['models'].values())[0]
    test_images = inference_setup['test_images'][:3]  # 使用前3张图片
    
    print("开始批量推理演示...")
    batch_stats = batch_inference_with_stats(first_model, test_images)
else:
    print(" 跳过批量推理演示")
    batch_stats = None

def compare_models_on_same_image(models_dict, image_path, conf_threshold=0.25, iou_threshold=0.45):
    """在同一张图片上对比多个模型的效果"""
    print(f" 模型对比展示\n")
    print(f"测试图片: {os.path.basename(image_path)}")
    print(f"对比模型数量: {len(models_dict)}")
    
    # 存储所有模型的结果
    model_results = {}
    
    # 为每个模型进行推理
    for model_name, model_path in models_dict.items():
        print(f"\n推理模型: {model_name}")
        
        try:
            # 加载模型
            model = load_yolov5_model(model_path)
            if model is None:
                continue
            
            model.conf = conf_threshold
            model.iou = iou_threshold
            
            # 执行推理
            start_time = time.time()
            results = model(image_path)
            inference_time = time.time() - start_time
            
            # 解析结果
            detections = results.pandas().xyxy[0]
            
            model_results[model_name] = {
                'results': results,
                'detections': detections,
                'inference_time': inference_time,
                'model_names': model.names
            }
            
            print(f"   检测到 {len(detections)} 个目标，耗时 {inference_time:.3f}s")
            
        except Exception as e:
            print(f"   推理失败: {e}")
            continue
    
    if not model_results:
        print("没有成功的推理结果")
        return None
    
    # 创建对比可视化
    create_model_comparison_visualization(image_path, model_results)
    
    # 生成对比统计表
    generate_comparison_statistics(model_results)
    
    return model_results

def create_model_comparison_visualization(image_path, model_results):
    """创建模型对比可视化"""
    num_models = len(model_results)
    
    # 计算子图布局
    if num_models <= 2:
        rows, cols = 1, num_models + 1  # +1 for original image
        figsize = (6 * (num_models + 1), 6)
    elif num_models <= 4:
        rows, cols = 2, 3  # 2x3 grid, first cell for original
        figsize = (18, 12)
    else:
        rows = (num_models + 2) // 3  # +2 for original and rounding
        cols = 3
        figsize = (18, 6 * rows)
    
    fig, axes = plt.subplots(rows, cols, figsize=figsize)
    if num_models == 1:
        axes = [axes]  # 确保axes是列表
    elif rows == 1:
        axes = axes.flatten() if hasattr(axes, 'flatten') else [axes]
    else:
        axes = axes.flatten()
    
    # 读取原图
    original_img = cv2.imread(image_path)
    original_img = cv2.cvtColor(original_img, cv2.COLOR_BGR2RGB)
    
    # 显示原图
    axes[0].imshow(original_img)
    axes[0].set_title('原始图片', fontsize=12, fontweight='bold')
    axes[0].axis('off')
    
    # 为每个模型显示结果
    for idx, (model_name, result_data) in enumerate(model_results.items()):
        ax_idx = idx + 1
        
        # 获取渲染后的图片
        result_img = result_data['results'].render()[0]
        
        axes[ax_idx].imshow(result_img)
        
        # 设置标题，包含检测统计
        num_detections = len(result_data['detections'])
        inference_time = result_data['inference_time']
        
        title = f"{model_name}\n{num_detections} 个目标, {inference_time:.3f}s"
        axes[ax_idx].set_title(title, fontsize=10, fontweight='bold')
        axes[ax_idx].axis('off')
    
    # 隐藏多余的子图
    for idx in range(len(model_results) + 1, len(axes)):
        axes[idx].axis('off')
    
    plt.tight_layout()
    plt.suptitle(f' 模型效果对比 - {os.path.basename(image_path)}', 
                fontsize=16, fontweight='bold', y=1.02)
    plt.show()

def generate_comparison_statistics(model_results):
    """生成模型对比统计表"""
    print("\n模型对比统计表")
    print("=" * 80)
    
    # 表头
    header = f"{'模型':<20} {'检测数量':<10} {'推理时间(s)':<12} {'平均置信度':<12} {'最高置信度':<12}"
    print(header)
    print("=" * 80)
    
    # 收集统计数据
    comparison_data = []
    
    for model_name, result_data in model_results.items():
        detections = result_data['detections']
        inference_time = result_data['inference_time']
        
        num_detections = len(detections)
        avg_confidence = detections['confidence'].mean() if len(detections) > 0 else 0
        max_confidence = detections['confidence'].max() if len(detections) > 0 else 0
        
        # 打印统计行
        row = f"{model_name:<20} {num_detections:<10} {inference_time:<12.3f} {avg_confidence:<12.3f} {max_confidence:<12.3f}"
        print(row)
        
        comparison_data.append({
            'model': model_name,
            'detections': num_detections,
            'inference_time': inference_time,
            'avg_confidence': avg_confidence,
            'max_confidence': max_confidence
        })
    
    print("=" * 80)
    
    # 找出最佳表现
    if comparison_data:
        best_detections = max(comparison_data, key=lambda x: x['detections'])
        fastest_model = min(comparison_data, key=lambda x: x['inference_time'])
        highest_confidence = max(comparison_data, key=lambda x: x['avg_confidence'])
        
        print("\n最佳表现:")
        print(f"   最多检测: {best_detections['model']} ({best_detections['detections']} 个)")
        print(f"   最快推理: {fastest_model['model']} ({fastest_model['inference_time']:.3f}s)")
        print(f"   最高置信度: {highest_confidence['model']} ({highest_confidence['avg_confidence']:.3f})")
    
    return comparison_data

# 执行模型对比演示
if inference_setup and len(inference_setup['models']) > 1 and len(inference_setup['test_images']) > 0:
    print("开始模型对比演示...")
    
    # 选择一张测试图片
    test_image = inference_setup['test_images'][0]
    
    # 对比所有可用模型
    comparison_results = compare_models_on_same_image(
        inference_setup['models'], 
        test_image,
        conf_threshold=0.25,
        iou_threshold=0.45
    )
else:
    print(" 跳过模型对比演示，需要至少2个模型")
    comparison_results = None

def interactive_parameter_tuning(model_path, image_path):
    """交互式推理参数调节"""
    print(f"交互式推理参数调节\n")
    print(f"测试图片: {os.path.basename(image_path)}")
    print(f"使用模型: {os.path.basename(model_path)}")
    
    # 加载模型
    model = load_yolov5_model(model_path)
    if model is None:
        return None
    
    # 定义参数范围
    conf_thresholds = [0.1, 0.25, 0.5, 0.75, 0.9]
    iou_thresholds = [0.3, 0.45, 0.6, 0.75, 0.9]
    
    print(f"\n 测试不同参数组合...")
    print(f"   置信度阈值: {conf_thresholds}")
    print(f"   NMS阈值: {iou_thresholds}")
    
    # 存储结果
    parameter_results = []
    
    # 测试不同参数组合
    for conf_thresh in conf_thresholds:
        for iou_thresh in iou_thresholds:
            try:
                # 设置参数
                model.conf = conf_thresh
                model.iou = iou_thresh
                
                # 执行推理
                start_time = time.time()
                results = model(image_path)
                inference_time = time.time() - start_time
                
                # 解析结果
                detections = results.pandas().xyxy[0]
                
                parameter_results.append({
                    'conf_threshold': conf_thresh,
                    'iou_threshold': iou_thresh,
                    'num_detections': len(detections),
                    'inference_time': inference_time,
                    'avg_confidence': detections['confidence'].mean() if len(detections) > 0 else 0,
                    'results': results,
                    'detections': detections
                })
                
            except Exception as e:
                print(f"   参数组合 (conf={conf_thresh}, iou={iou_thresh}) 失败: {e}")
                continue
    
    if not parameter_results:
        print("没有成功的参数测试结果")
        return None
    
    # 创建参数效果可视化
    create_parameter_effect_visualization(parameter_results)
    
    # 显示最佳参数组合
    find_optimal_parameters(parameter_results)
    
    # 展示不同参数下的检测结果
    show_parameter_comparison_results(image_path, parameter_results)
    
    return parameter_results

def create_parameter_effect_visualization(parameter_results):
    """创建参数效果可视化"""
    # 转换为DataFrame便于分析
    import pandas as pd
    df = pd.DataFrame(parameter_results)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 置信度阈值对检测数量的影响
    conf_groups = df.groupby('conf_threshold')['num_detections'].mean()
    ax1.plot(conf_groups.index, conf_groups.values, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('置信度阈值')
    ax1.set_ylabel('平均检测数量')
    ax1.set_title('置信度阈值 vs 检测数量')
    ax1.grid(True, alpha=0.3)
    
    # 2. NMS阈值对检测数量的影响
    iou_groups = df.groupby('iou_threshold')['num_detections'].mean()
    ax2.plot(iou_groups.index, iou_groups.values, 'ro-', linewidth=2, markersize=8)
    ax2.set_xlabel('NMS阈值')
    ax2.set_ylabel('平均检测数量')
    ax2.set_title('NMS阈值 vs 检测数量')
    ax2.grid(True, alpha=0.3)
    
    # 3. 参数组合热力图
    pivot_table = df.pivot_table(values='num_detections', 
                                index='conf_threshold', 
                                columns='iou_threshold', 
                                aggfunc='mean')
    
    im = ax3.imshow(pivot_table.values, cmap='YlOrRd', aspect='auto')
    ax3.set_xticks(range(len(pivot_table.columns)))
    ax3.set_yticks(range(len(pivot_table.index)))
    ax3.set_xticklabels([f'{x:.2f}' for x in pivot_table.columns])
    ax3.set_yticklabels([f'{y:.2f}' for y in pivot_table.index])
    ax3.set_xlabel('NMS阈值')
    ax3.set_ylabel('置信度阈值')
    ax3.set_title('参数组合热力图\n(颜色越深检测数量越多)')
    
    # 添加数值标注
    for i in range(len(pivot_table.index)):
        for j in range(len(pivot_table.columns)):
            text = ax3.text(j, i, f'{pivot_table.iloc[i, j]:.0f}',
                           ha="center", va="center", color="black", fontweight='bold')
    
    # 4. 推理时间分布
    ax4.hist(df['inference_time'], bins=15, alpha=0.7, color='lightblue', edgecolor='black')
    ax4.set_xlabel('推理时间 (秒)')
    ax4.set_ylabel('频次')
    ax4.set_title('推理时间分布')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.suptitle('推理参数效果分析', fontsize=16, fontweight='bold', y=1.02)
    plt.show()

def find_optimal_parameters(parameter_results):
    """寻找最优参数组合"""
    print("\n最优参数分析:")
    
    # 按不同标准找最优参数
    max_detections = max(parameter_results, key=lambda x: x['num_detections'])
    fastest_inference = min(parameter_results, key=lambda x: x['inference_time'])
    highest_confidence = max(parameter_results, key=lambda x: x['avg_confidence'])
    
    print(f"\n不同优化目标的最优参数:")
    print(f"   最多检测数量:")
    print(f"      参数: conf={max_detections['conf_threshold']}, iou={max_detections['iou_threshold']}")
    print(f"      结果: {max_detections['num_detections']} 个检测")
    
    print(f"   最快推理速度:")
    print(f"      参数: conf={fastest_inference['conf_threshold']}, iou={fastest_inference['iou_threshold']}")
    print(f"      结果: {fastest_inference['inference_time']:.3f} 秒")
    
    print(f"   最高平均置信度:")
    print(f"      参数: conf={highest_confidence['conf_threshold']}, iou={highest_confidence['iou_threshold']}")
    print(f"      结果: {highest_confidence['avg_confidence']:.3f}")
    
    # 综合评分（简单的加权平均）
    for result in parameter_results:
        # 标准化各项指标到0-1范围
        max_det = max(r['num_detections'] for r in parameter_results)
        min_time = min(r['inference_time'] for r in parameter_results)
        max_conf = max(r['avg_confidence'] for r in parameter_results)
        
        det_score = result['num_detections'] / max_det if max_det > 0 else 0
        time_score = min_time / result['inference_time'] if result['inference_time'] > 0 else 0
        conf_score = result['avg_confidence'] / max_conf if max_conf > 0 else 0
        
        # 综合评分（可以调整权重）
        result['composite_score'] = 0.4 * det_score + 0.3 * time_score + 0.3 * conf_score
    
    best_overall = max(parameter_results, key=lambda x: x['composite_score'])
    print(f"\n综合最优参数:")
    print(f"      参数: conf={best_overall['conf_threshold']}, iou={best_overall['iou_threshold']}")
    print(f"      综合评分: {best_overall['composite_score']:.3f}")
    print(f"      检测数量: {best_overall['num_detections']}")
    print(f"      推理时间: {best_overall['inference_time']:.3f}s")
    print(f"      平均置信度: {best_overall['avg_confidence']:.3f}")

# 执行交互式参数调节演示
if inference_setup and len(inference_setup['models']) > 0 and len(inference_setup['test_images']) > 0:
    print("开始交互式参数调节演示...")
    
    first_model = list(inference_setup['models'].values())[0]
    first_image = inference_setup['test_images'][0]
    
    parameter_tuning_results = interactive_parameter_tuning(first_model, first_image)
else:
    print(" 跳过交互式参数调节演示")
    parameter_tuning_results = None

def deployment_preparation_guide():
    """模型部署准备指南"""
    print("模型部署准备指南\n")
    
    print("部署前检查清单:")
    print("\n1. 模型性能验证")
    print("   在验证集上的mAP指标")
    print("   推理速度满足业务需求")
    print("   内存占用在可接受范围")
    print("   不同输入尺寸的稳定性")
    
    print("\n2.  模型优化")
    print("   模型格式转换 (PyTorch → ONNX → TensorRT)")
    print("   推理加速 (FP16混合精度、批处理)")
    print("   模型压缩 (量化、剪枝、知识蒸馏)")
    print("   参数调优 (置信度、NMS阈值)")
    
    print("\n3. 部署环境")
    print("    硬件要求 (CPU、GPU、内存)")
    print("   依赖管理 (Python版本、库版本)")
    print("   容器化 (Docker镜像)")
    print("    云服务 (AWS、Azure、GCP)")
    
    print("\n4. 监控与维护")
    print("   性能监控 (推理时间、吞吐量)")
    print("   准确性监控 (检测质量、误报率)")
    print("   模型更新策略")
    print("   异常处理和报警")
    
    # 生成部署配置示例
    generate_deployment_configs()
    
    # 性能基准测试
    if inference_setup and len(inference_setup['models']) > 0:
        perform_deployment_benchmark()

def generate_deployment_configs():
    """生成部署配置文件示例"""
    print("\n生成部署配置文件...")
    
    # Docker配置
    dockerfile_content = '''
# YOLOv5足球检测模型部署
FROM ultralytics/yolov5:latest

# 设置工作目录
WORKDIR /app

# 复制模型文件
COPY best.pt /app/
COPY football_dataset.yaml /app/

# 复制推理脚本
COPY inference_api.py /app/

# 安装额外依赖
RUN pip install fastapi uvicorn

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "inference_api:app", "--host", "0.0.0.0", "--port", "8000"]
'''
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    print("   已生成 Dockerfile")
    
    # API服务配置
    api_content = '''
from fastapi import FastAPI, File, UploadFile
from fastapi.responses import JSONResponse
import torch
import cv2
import numpy as np
from PIL import Image
import io

app = FastAPI(title="YOLOv5足球检测API")

# 加载模型
model = torch.hub.load('ultralytics/yolov5', 'custom', path='best.pt')
model.conf = 0.25
model.iou = 0.45

@app.post("/detect")
async def detect_objects(file: UploadFile = File(...)):
    try:
        # 读取图片
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 执行推理
        results = model(image)
        
        # 解析结果
        detections = results.pandas().xyxy[0].to_dict('records')
        
        return JSONResponse({
            "status": "success",
            "detections": detections,
            "count": len(detections)
        })
        
    except Exception as e:
        return JSONResponse({
            "status": "error",
            "message": str(e)
        }, status_code=500)

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
'''
    
    with open('inference_api.py', 'w') as f:
        f.write(api_content)
    print("   已生成 inference_api.py")
    
    # 部署脚本
    deploy_script = '''
#!/bin/bash
# YOLOv5模型部署脚本

echo "开始部署YOLOv5足球检测模型"

# 构建Docker镜像
echo "构建Docker镜像..."
docker build -t yolov5-football-detection .

# 停止现有容器
echo "停止现有容器..."
docker stop yolov5-app 2>/dev/null || true
docker rm yolov5-app 2>/dev/null || true

# 启动新容器
echo "启动新容器..."
docker run -d \
  --name yolov5-app \
  -p 8000:8000 \
  --restart unless-stopped \
  yolov5-football-detection

echo "部署完成！API服务运行在 http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
'''
    
    with open('deploy.sh', 'w') as f:
        f.write(deploy_script)
    print("   已生成 deploy.sh")
    
    print("\n使用方法:")
    print("   1. 将最佳模型权重复制为 best.pt")
    print("   2. 运行: bash deploy.sh")
    print("   3. 访问: http://localhost:8000/docs")

def perform_deployment_benchmark():
    """执行部署基准测试"""
    print("\n部署性能基准测试")
    
    if not inference_setup or len(inference_setup['models']) == 0:
        print("    没有可用模型进行基准测试")
        return
    
    first_model = list(inference_setup['models'].values())[0]
    
    try:
        # 加载模型
        model = load_yolov5_model(first_model)
        if model is None:
            return
        
        # 创建测试数据
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        
        # 预热
        print("   模型预热...")
        for _ in range(5):
            _ = model(test_image)
        
        # 基准测试
        print("   执行基准测试...")
        times = []
        for i in range(100):
            start_time = time.time()
            _ = model(test_image)
            times.append(time.time() - start_time)
        
        # 统计结果
        avg_time = np.mean(times)
        std_time = np.std(times)
        min_time = np.min(times)
        max_time = np.max(times)
        fps = 1.0 / avg_time
        
        print(f"\n基准测试结果:")
        print(f"   平均推理时间: {avg_time:.3f} ± {std_time:.3f} 秒")
        print(f"   最快推理时间: {min_time:.3f} 秒")
        print(f"   最慢推理时间: {max_time:.3f} 秒")
        print(f"   推理速度(FPS): {fps:.1f}")
        print(f"   吞吐量估算: {fps * 3600:.0f} 张/小时")
        
        # 部署建议
        print(f"\n部署建议:")
        if fps >= 30:
            print("   性能优秀，适合实时应用")
        elif fps >= 10:
            print("   性能良好，适合准实时应用")
        elif fps >= 1:
            print("    性能一般，适合批处理应用")
        else:
            print("   性能较差，建议优化模型或硬件")
        
    except Exception as e:
        print(f"   基准测试失败: {e}")

# 执行部署准备
deployment_preparation_guide()