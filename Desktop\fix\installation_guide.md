# YOLOv5足球检测实训项目 - 环境安装指南

## 系统要求

- **操作系统**: Windows 11
- **Python版本**: 3.11.9
- **GPU**: NVIDIA GPU（推荐，支持CUDA 11.8）
- **内存**: 至少8GB RAM（推荐16GB+）
- **存储**: 至少10GB可用空间

## 快速安装

### 1. 创建虚拟环境（推荐）

```bash
# 使用conda创建环境
conda create -n yolov5-football python=3.11.9
conda activate yolov5-football

# 或使用venv创建环境
python -m venv yolov5-football
yolov5-football\Scripts\activate  # Windows
```

### 2. 安装依赖

```bash
# 单独安装 PyTorch
# 执行以下命令来正确安装与 CUDA 11.8 匹配的 PyTorch, torchvision, 和 torchaudio。
pip install torch==2.1.2 torchvision==0.16.2 torchaudio==2.1.2 --extra-index-url https://download.pytorch.org/whl/cu118

# 安装所有依赖
pip install -r requirements.txt

# 如果遇到网络问题，可以使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. 验证安装

```python
# 验证PyTorch和CUDA
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"CUDA版本: {torch.version.cuda}")

# 验证YOLOv5
import yolov5
print(f"YOLOv5版本: {yolov5.__version__}")

# 验证OpenCV
import cv2
print(f"OpenCV版本: {cv2.__version__}")
```

## 常见问题和解决方案

### 问题1: PyTorch CUDA版本不匹配

**症状**: `torch.cuda.is_available()` 返回 `False`

**解决方案**:

```bash
# 卸载现有PyTorch
pip uninstall torch torchvision torchaudio

# 重新安装CUDA版本
pip install torch==2.1.2+cu118 torchvision==0.16.2+cu118 torchaudio==2.1.2+cu118 --extra-index-url https://download.pytorch.org/whl/cu118
```

### 问题2: OpenCV安装失败

**解决方案**:

```bash
# 先安装Microsoft Visual C++ Redistributable
# 然后重新安装OpenCV
pip install opencv-python==******** --force-reinstall
```

### 问题3: 某些包版本冲突

**解决方案**:

```bash
# 使用pip-tools解决依赖冲突
pip install pip-tools
pip-compile requirements.txt
pip-sync requirements.txt
```

## 可选组件安装

### TensorRT（高性能推理）

```bash
# 需要先安装NVIDIA TensorRT
# 下载地址: https://developer.nvidia.com/tensorrt
# 然后安装Python包
pip install tensorrt==8.6.1
```

### 开发工具

```bash
# 代码格式化和检查
pip install black flake8 isort

# 性能分析工具
pip install py-spy scalene
```

## 依赖包说明

### 核心框架

- **torch**: PyTorch深度学习框架
- **ultralytics**: YOLOv5官方实现
- **opencv-python**: 计算机视觉库

### 数据处理

- **pandas**: 数据分析库
- **numpy**: 数值计算库
- **matplotlib**: 可视化库

### 模型部署

- **onnx**: 模型格式转换
- **fastapi**: API服务框架
- **uvicorn**: ASGI服务器

### 目标追踪

- **filterpy**: 卡尔曼滤波
- **lap**: 线性分配问题求解
- **motmetrics**: 多目标追踪评估

## 安装验证脚本

创建 `verify_installation.py` 文件：

```python
#!/usr/bin/env python3
"""
YOLOv5足球检测项目环境验证脚本
"""

import sys
import importlib
import torch
import cv2
import numpy as np

def check_package(package_name, version_attr='__version__'):
    """检查包是否正确安装"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, version_attr, 'Unknown')
        print(f"{package_name}: {version}")
        return True
    except ImportError:
        print(f"{package_name}: 未安装")
        return False

def main():
    print("YOLOv5足球检测项目环境检查\n")
  
    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {python_version}")
  
    if sys.version_info < (3, 11):
        print(" 警告: 推荐使用Python 3.11+")
  
    print("\n核心依赖检查:")
  
    # 核心包检查
    packages = [
        'torch', 'torchvision', 'ultralytics', 'yolov5',
        'cv2', 'numpy', 'pandas', 'matplotlib', 'seaborn',
        'jupyter', 'fastapi', 'onnx', 'onnxruntime'
    ]
  
    failed_packages = []
    for package in packages:
        if not check_package(package):
            failed_packages.append(package)
  
    # CUDA检查
    print(f"\n CUDA支持:")
    print(f"   CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   CUDA版本: {torch.version.cuda}")
        print(f"   GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
  
    # 总结
    print(f"\n检查结果:")
    print(f"   成功: {len(packages) - len(failed_packages)}/{len(packages)}")
  
    if failed_packages:
        print(f"   失败的包: {', '.join(failed_packages)}")
        print(f"\n 请运行以下命令安装缺失的包:")
        print(f"   pip install {' '.join(failed_packages)}")
        return False
    else:
        print("   所有依赖都已正确安装！")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

运行验证：

```bash
python verify_installation.py
```

## 更新依赖

定期更新依赖以获得最新功能和安全修复：

```bash
# 更新所有包到最新版本
pip install --upgrade -r requirements.txt

# 或者更新特定包
pip install --upgrade torch torchvision ultralytics
```

## 获取帮助

如果遇到安装问题，可以：

1. 查看项目文档和FAQ
2. 在GitHub Issues中搜索相关问题
3. 联系项目维护者
4. 参考官方文档：
   - [PyTorch安装指南](https://pytorch.org/get-started/locally/)
   - [YOLOv5文档](https://docs.ultralytics.com/)
   - [OpenCV安装指南](https://docs.opencv.org/4.x/d5/de5/tutorial_py_setup_in_windows.html)
