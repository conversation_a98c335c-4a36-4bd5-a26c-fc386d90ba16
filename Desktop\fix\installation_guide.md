# YOLOv5足球检测实训项目 - 环境安装指南（精简版）

## 系统要求

- **操作系统**: Windows 11
- **Python版本**: 3.11.9
- **GPU**: NVIDIA GPU（推荐，支持CUDA 11.8）
- **内存**: 至少8GB RAM（推荐16GB+）
- **存储**: 至少5GB可用空间（精简版依赖更少）

## 快速安装

### 1. 创建虚拟环境（推荐）

```bash
# 使用conda创建环境
conda create -n yolov5-football python=3.11.9
conda activate yolov5-football

# 或使用venv创建环境
python -m venv yolov5-football
yolov5-football\Scripts\activate  # Windows
```

### 2. 安装依赖（精简版）

```bash
# 步骤1: 单独安装 PyTorch（必须先安装）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 步骤2: 安装精简版依赖（仅12个核心包）
pip install -r requirements.txt

# 如果遇到网络问题，可以使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. 验证安装

```python
# 验证PyTorch和CUDA
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA可用: {torch.cuda.is_available()}")
print(f"CUDA版本: {torch.version.cuda}")

# 验证Ultralytics（新的YOLOv5接口）
from ultralytics import YOLO
print("Ultralytics导入成功")

# 验证OpenCV
import cv2
print(f"OpenCV版本: {cv2.__version__}")

# 验证其他核心包
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
print("所有核心依赖验证成功！")
```

## 常见问题和解决方案

### 问题1: PyTorch CUDA版本不匹配

**症状**: `torch.cuda.is_available()` 返回 `False`

**解决方案**:

```bash
# 卸载现有PyTorch
pip uninstall torch torchvision torchaudio

# 重新安装CUDA版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 问题2: OpenCV安装失败

**解决方案**:

```bash
# 先安装Microsoft Visual C++ Redistributable
# 然后重新安装OpenCV
pip install opencv-contrib-python==******** --force-reinstall
```

### 问题3: Ultralytics导入失败

**症状**: `ImportError: No module named 'ultralytics'`

**解决方案**:

```bash
# 安装最新版本的ultralytics
pip install ultralytics>=8.0.0

# 如果仍有问题，尝试升级pip
pip install --upgrade pip
pip install ultralytics>=8.0.0
```

### 问题4: 依赖版本冲突（精简版很少出现）

**解决方案**:

```bash
# 精简版依赖冲突较少，如有问题可重新创建环境
conda deactivate
conda remove -n yolov5-football --all
conda create -n yolov5-football python=3.11.9
conda activate yolov5-football
# 重新按步骤安装
```

## 精简版依赖包说明

### 核心依赖（12个包）

**深度学习和计算机视觉:**
- **torch**: PyTorch深度学习框架（需单独安装）
- **ultralytics**: YOLOv5/YOLOv8统一接口（替代原yolov5包）
- **opencv-contrib-python**: 计算机视觉库
- **Pillow**: 图像处理库

**数据处理和分析:**
- **numpy**: 数值计算基础库
- **pandas**: 数据分析和处理
- **scikit-learn**: 机器学习工具（仅使用confusion_matrix）

**数据可视化:**
- **matplotlib**: 基础绘图和可视化
- **seaborn**: 统计数据可视化

**配置和网络:**
- **PyYAML**: YAML配置文件处理
- **requests**: HTTP请求处理

**系统和API:**
- **psutil**: 系统信息获取
- **fastapi**: Web API开发框架

## 可选扩展组件

如需要额外功能，可按需安装以下组件：

### 模型部署和优化
```bash
pip install onnx onnxruntime-gpu onnx-simplifier
```

### 目标追踪功能
```bash
pip install filterpy motmetrics munkres
```

### 开发和调试工具
```bash
pip install tqdm jupyter notebook ipywidgets
```

### 性能分析工具
```bash
pip install memory-profiler line-profiler
```

## 精简版安装验证脚本

创建 `verify_installation.py` 文件：

```python
#!/usr/bin/env python3
"""
YOLOv5足球检测项目环境验证脚本（精简版）
"""

import sys
import importlib

def check_package(package_name, version_attr='__version__'):
    """检查包是否正确安装"""
    try:
        module = importlib.import_module(package_name)
        version = getattr(module, version_attr, 'Unknown')
        print(f"✓ {package_name}: {version}")
        return True
    except ImportError:
        print(f"✗ {package_name}: 未安装")
        return False

def main():
    print("YOLOv5足球检测项目环境检查（精简版）\n")

    # 检查Python版本
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"Python版本: {python_version}")

    if sys.version_info < (3, 11):
        print("⚠️  警告: 推荐使用Python 3.11+")

    print("\n核心依赖检查（12个包）:")

    # 精简版核心包检查
    core_packages = [
        'torch', 'torchvision', 'ultralytics', 'cv2',
        'numpy', 'pandas', 'sklearn', 'matplotlib',
        'seaborn', 'yaml', 'requests', 'psutil', 'fastapi'
    ]

    failed_packages = []
    for package in core_packages:
        if not check_package(package):
            failed_packages.append(package)

    # CUDA检查
    try:
        import torch
        print(f"\nCUDA支持:")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError:
        print("\n⚠️  PyTorch未安装，无法检查CUDA支持")

    # 总结
    print(f"\n检查结果:")
    print(f"   成功: {len(core_packages) - len(failed_packages)}/{len(core_packages)}")

    if failed_packages:
        print(f"   失败的包: {', '.join(failed_packages)}")
        print(f"\n请运行以下命令安装缺失的包:")
        print(f"   pip install {' '.join(failed_packages)}")
        return False
    else:
        print("   🎉 所有核心依赖都已正确安装！")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
```

运行验证：

```bash
python verify_installation.py
```

## 更新依赖

精简版依赖更新更简单快速：

```bash
# 更新所有核心包到最新版本
pip install --upgrade -r requirements.txt

# 或者更新特定包
pip install --upgrade torch torchvision ultralytics

# 检查过时的包
pip list --outdated
```

## 精简版优势

相比完整版依赖，精简版具有以下优势：

- **安装速度快**: 仅12个核心包，安装时间减少80%+
- **体积小**: 总安装大小减少约60%
- **冲突少**: 依赖冲突概率大幅降低
- **维护简单**: 更新和管理更容易
- **启动快**: 项目启动时间更短

## 获取帮助

如果遇到安装问题，可以：

1. 查看项目文档和FAQ
2. 运行验证脚本诊断问题
3. 检查是否使用了精简版requirements.txt
4. 参考官方文档：
   - [PyTorch安装指南](https://pytorch.org/get-started/locally/)
   - [Ultralytics文档](https://docs.ultralytics.com/)
   - [OpenCV安装指南](https://docs.opencv.org/4.x/d5/de5/tutorial_py_setup_in_windows.html)

## 从完整版迁移到精简版

如果您之前使用完整版依赖，建议：

```bash
# 1. 备份当前环境
conda env export > backup_environment.yml

# 2. 创建新的精简环境
conda create -n yolov5-football-slim python=3.11.9
conda activate yolov5-football-slim

# 3. 安装精简版依赖
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt

# 4. 验证安装
python verify_installation.py
```
