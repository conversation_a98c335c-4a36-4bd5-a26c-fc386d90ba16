# ================================
# YOLOv5足球检测实训项目 - 精简版依赖文件
# ================================
# 适用于Windows 11系统，Python 3.11.9

#
# 安装说明：
# 1. 首先安装PyTorch: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# 2. 然后安装本文件: pip install -r requirements.txt
# ================================

# ================================
# 基础数值计算（必须先安装）
# ================================
numpy==1.24.4

# ================================
# 深度学习和计算机视觉核心
# ================================
# 注意：PyTorch需要根据CUDA版本单独安装，请参考安装指南
ultralytics>=8.0.0  # YOLOv5/YOLOv8统一接口，替代原yolov5包
opencv-contrib-python==********  # 计算机视觉处理 (cv2)
Pillow==10.1.0  # 图像处理 (PIL)

# ================================
# 数据处理和分析
# ================================
pandas==2.1.4  # 数据处理和分析
scikit-learn==1.3.2  # 机器学习工具（仅使用confusion_matrix）

# ================================
# 数据可视化
# ================================
matplotlib==3.8.2  # 基础绘图和可视化
seaborn==0.13.0  # 统计数据可视化

# ================================
# 配置文件和网络
# ================================
PyYAML==6.0.1  # YAML配置文件处理
requests==2.31.0  # HTTP请求处理

# ================================
# 系统信息和API开发
# ================================
psutil==5.9.6  # 系统信息获取
fastapi==0.104.1  # Web API开发框架

# ================================
# 说明
# ================================
#
# 如需要额外功能，可按需添加：
# - ONNX模型优化：onnx, onnxruntime-gpu
# - 目标追踪：filterpy, motmetrics
# - 进度条：tqdm
# - Jupyter环境：jupyter, notebook
# ================================