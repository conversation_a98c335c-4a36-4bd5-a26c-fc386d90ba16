# ================================
# 必先安装的基础依赖
# ================================
numpy==1.24.4  # 必须先安装，防止某些包编译失败

# YOLOv5足球检测实训项目依赖文件 (修正版)
# 适用于Windows 11系统，Python 3.11.9
# 请注意：PyTorch, Torchvision, Torchaudio 需要单独安装以匹配您的CUDA版本

# ================================
# YOLOv5核心依赖
# ================================
yolov5==7.0.13

# 模型相关
timm==0.9.12
thop==0.1.1-2209072238

# ================================
# 计算机视觉和图像处理
# ================================
opencv-contrib-python==********
Pillow==10.1.0
imageio==2.33.1
scikit-image==0.22.0
albumentations==1.3.1

# ================================
# 数据处理和科学计算
# ================================
pandas==2.1.4
scipy==1.11.4
scikit-learn==1.3.2

# ================================
# 可视化和绘图
# ================================
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
bokeh==3.3.2

# ================================
# Jupyter和开发环境
# ================================
jupyter==1.0.0
jupyterlab==4.0.9
notebook==7.0.6
ipywidgets==8.1.1
ipykernel==6.27.1

# ================================
# 进度条和日志
# ================================
tqdm==4.66.1
colorama==0.4.6
termcolor==2.4.0

# ================================
# 配置文件处理
# ================================
PyYAML==6.0.1
omegaconf==2.3.0

# ================================
# 模型部署和优化（挑战二）
# ================================
onnx==1.15.0
onnxruntime-gpu==1.16.3
onnx-simplifier==0.4.35
onnxoptimizer==0.3.13

# ================================
# API开发和Web服务
# ================================
fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
aiofiles==23.2.1
jinja2==3.1.2
python-jose==3.3.0

# ================================
# 目标追踪（挑战一）
# ================================
filterpy==1.4.5
lap @ https://download.lfd.uci.edu/pythonlibs/archived/lap-0.4.0-cp311-cp311-win_amd64.whl
cython-bbox==0.1.3
motmetrics==1.4.0
munkres==1.1.4

# ================================
# HTTP请求和网络
# ================================
requests==2.31.0
urllib3==2.1.0
aiohttp==3.9.1

# ================================
# 文件处理和压缩
# ================================
zipfile-deflate64==0.2.0
rarfile==4.1

# ================================
# 系统监控和性能分析
# ================================
psutil==5.9.6
GPUtil==1.4.0
py-cpuinfo==9.0.0

# ================================
# 测试和代码质量
# ================================
pytest==7.4.3
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0

# ================================
# 数据库和存储（可选）
# ================================
sqlalchemy==2.0.23

# ================================
# 其他实用工具
# ================================
glob2==0.7
typing-extensions==4.8.0

# ================================
# Git和版本控制工具
# ================================
gitpython==3.1.40

# ================================
# 环境和包管理
# ================================
pip==23.3.1
setuptools==69.0.2
wheel==0.42.0

# ================================
# 安全和加密
# ================================
cryptography==41.0.8
certifi==2023.11.17

# ================================
# 时间和日期处理
# ================================
python-dateutil==2.8.2
pytz==2023.3.post1

# ================================
# 内存和性能优化
# ================================
memory-profiler==0.61.0
line-profiler==4.1.1

# ================================
# 多进程和并发
# ================================
joblib==1.3.2

# ================================
# 字符串和文本处理
# ================================
regex==2023.10.3
fuzzywuzzy==0.18.0
python-levenshtein==0.23.0

# ================================
# 数学和统计
# ================================
statsmodels==0.14.0
sympy==1.12

# ================================
# 缓存和序列化
# ================================
dill==0.3.7
cloudpickle==3.0.0

# ================================
# 网络和URL处理
# ================================
validators==0.22.0